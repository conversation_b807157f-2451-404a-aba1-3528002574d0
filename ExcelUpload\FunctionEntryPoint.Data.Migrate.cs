﻿using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Request.Bulk_Upload;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Reports.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.DataManagement;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Collections.Concurrent;
using System.Data;
using System.Text.RegularExpressions;
using static Lrb.Application.DataManagement.Web.Specs.GetProspectByContactNoSpecs;

namespace ExcelUpload
{
    public class MigrateBulkDatadataUploadTrackerUsingEPPlus
    {
        public string? S3BucketKey { get; set; }
        public Dictionary<DataMigrateColumns, string>? MappedColumnsData { get; set; }
        public List<string>? UserIds { get; set; }
        public Guid TrackerId { get; set; }
    }
    public partial class FunctionEntryPoint : IFunctionEntryPoint
    {
        public async Task MigrateDataHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            DataMigrateTracker? dataMigrateTracker = await _dataMigrateTrackerRepo.GetByIdAsync(input.TrackerId);
            Console.WriteLine($"handler() -> BulkDataUploadTracker GetById(): {JsonConvert.SerializeObject(dataMigrateTracker)}");
            try
            {
                if (dataMigrateTracker != null)
                {
                    try
                    {
                        dataMigrateTracker.MappedColumnsData = dataMigrateTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        dataMigrateTracker.Status = UploadStatus.Started;
                        dataMigrateTracker.LastModifiedBy = input.CurrentUserId;
                        dataMigrateTracker.CreatedBy = input.CurrentUserId;
                        var migrationType = dataMigrateTracker.MigrationType;
                        //var migrationType = LeadMigrationType.UpdateExistingLead;
                        await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);
                        Console.WriteLine($"handler() -> BulkDataUploadTracker Updated Status: {dataMigrateTracker.Status} \n {JsonConvert.SerializeObject(dataMigrateTracker)}");

                        #region Fetch all required MasterData and Other data
                        List<Project> tempProjects = new(await _newProjectRepo.ListAsync(cancellationToken));
                        List<Property> properties = new(await _propertyRepo.ListAsync(cancellationToken));
                        List<MasterPropertyType> propetyTypes = new(await _propertyTypeRepo.ListAsync(cancellationToken));
                        List<Lrb.Application.Identity.Users.UserDetailsDto> users = new(await _userService.GetListAsync(cancellationToken));
                        DuplicateLeadFeatureInfo? duplicateFeatureInfo = (await _duplicateFeatureInfo.ListAsync()).FirstOrDefault();
                        List<CustomProspectStatus> leadStatuses = new(await _prospectStatusRepo.ListAsync(CancellationToken.None));
                        List<Prospect> existingDatas = new();
                        var sources = await _prospectSourceRepo.ListAsync(cancellationToken);
                        List<Agency> agencies = new((await _agencyRepo.ListAsync(cancellationToken)).Where(i => !string.IsNullOrWhiteSpace(i.Name)));
                        List<ChannelPartner> channelPartner = new((await _cpRepository.ListAsync(cancellationToken)).Where(i => !string.IsNullOrWhiteSpace(i.FirmName)));
                        List<Campaign> campaigns = new((await _campaignRepo.ListAsync(cancellationToken)).Where(i => !string.IsNullOrWhiteSpace(i.Name)));


                        #endregion

                        #region Convert file to Datatable
                        DataTable dataTable = new();
                        List<InvalidProspect> invalids = new();
                        int totalRows = 0;
                        UploadType uploadType = UploadType.None;
                        if (!string.IsNullOrWhiteSpace(dataMigrateTracker.S3BucketKey))
                        {
                            Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", dataMigrateTracker.S3BucketKey);
                            if (dataMigrateTracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                            {
                                using MemoryStream memoryStream = new();
                                fileStream.CopyTo(memoryStream);
                                dataTable = CSVHelper.CSVToDataTable(memoryStream);
                                uploadType = UploadType.CSV;
                            }
                            else
                            {
                                dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, dataMigrateTracker.SheetName);
                                uploadType = UploadType.Excel;
                            }
                            totalRows = dataTable.Rows.Count;
                            for (int i = totalRows - 1; i >= 0; i--)
                            {
                                DataRow row = dataTable.Rows[i];
                                if (row.ItemArray.All(i => string.IsNullOrEmpty(i?.ToString())))
                                {
                                    row.Delete();
                                }
                                else if (string.IsNullOrEmpty(row[dataMigrateTracker.MappedColumnsData?[DataMigrateColumns.Name] ?? string.Empty].ToString()) && string.IsNullOrEmpty(row[dataMigrateTracker.MappedColumnsData?[DataMigrateColumns.ContactNo] ?? string.Empty].ToString()))
                                {
                                    var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i?.ToString())));
                                    var invalidProspect = new InvalidProspect
                                    {
                                        Errors = "Contact number and name are empty.",
                                        Notes = notes
                                    };
                                    if (!invalids.Any(i => i.Notes == invalidProspect.Notes))
                                    {
                                        invalids.Add(invalidProspect);
                                    }
                                    row.Delete();
                                }
                            }
                            if (dataTable.Rows.Count <= 0)
                            {
                                throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                            }
                            totalRows = dataTable.Rows.Count;
                            Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                        }
                        #endregion
                        #region checking For new Properties or projects
                        List<Property> newProperties = new();
                        List<Lrb.Domain.Entities.Project> newProjects = new();
                        List<Agency> newAgencies = new();
                        List<ChannelPartner> newChannels = new();
                        List<Campaign> newCampaign = new();

                        if (((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.Property) ?? false) && (dataMigrateTracker.MappedColumnsData[DataMigrateColumns.Property] != null))
                            || ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.Project) ?? false) && (dataMigrateTracker.MappedColumnsData[DataMigrateColumns.Project] != null))
                            || ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.AgencyName) ?? false) && (dataMigrateTracker.MappedColumnsData[DataMigrateColumns.AgencyName] != null))
                            || ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.ChannelPartnerName) ?? false) && (dataMigrateTracker.MappedColumnsData[DataMigrateColumns.ChannelPartnerName] != null))
                            || ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.CampaignName) ?? false) && (dataMigrateTracker.MappedColumnsData[DataMigrateColumns.CampaignName] != null)))
                        {
                            var existingPropertynames = properties.Where(i => i != null && !string.IsNullOrEmpty(i.Title)).Select(i => i.Title?.ToLower().Trim()).ToList();
                            var existingprojctnames = tempProjects.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                            var existingAgencyNames = agencies.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                            var exstingChannelPartners = channelPartner.Where(i => i != null && !string.IsNullOrEmpty(i.FirmName)).Select(i => i.FirmName?.ToLower().Trim()).ToList();
                            var exstingCampaigns = campaigns.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                            var isPropertyPresent = ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.Property) ?? false) && (dataMigrateTracker.MappedColumnsData[DataMigrateColumns.Property] != null));
                            var isProjectPresent = ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.Project) ?? false) && (dataMigrateTracker.MappedColumnsData[DataMigrateColumns.Project] != null));
                            var isAgencyNamePresent = ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.AgencyName) ?? false) && (dataMigrateTracker.MappedColumnsData[DataMigrateColumns.AgencyName] != null));
                            var isChannelPresent = ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.ChannelPartnerName) ?? false) && (dataMigrateTracker.MappedColumnsData[DataMigrateColumns.ChannelPartnerName] != null));
                            var isCampaignPresent = ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.CampaignName) ?? false) && (dataMigrateTracker.MappedColumnsData[DataMigrateColumns.CampaignName] != null));
                            dataTable.AsEnumerable().ToList().ForEach(row =>
                            {
                                if (isPropertyPresent)
                                {
                                    var propertyName = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.Property]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(propertyName) && !(existingPropertynames?.Contains(propertyName.ToLower().Trim()) ?? false) && !newProperties.Select(i => i.Title).Contains(propertyName))
                                    {
                                        newProperties.Add(new()
                                        {
                                            Title = propertyName.Trim(),
                                        });
                                    }
                                }
                                if (isProjectPresent)
                                {
                                    var projectName = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.Project]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(projectName) && !(existingprojctnames?.Contains(projectName.ToLower()) ?? false) && !newProjects.Select(i => i.Name).Contains(projectName))
                                    {
                                        newProjects.Add(new()
                                        {
                                            Name = projectName.Trim(),
                                        });
                                    }
                                }
                                if (isAgencyNamePresent)
                                {
                                    var agencyName = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.AgencyName]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(agencyName) && !(existingAgencyNames?.Contains(agencyName.ToLower()) ?? false) && !newAgencies.Select(i => i.Name).Contains(agencyName))
                                    {
                                        newAgencies.Add(new()
                                        {
                                            Name = agencyName,
                                            CreatedBy = dataMigrateTracker.CreatedBy,
                                            LastModifiedBy = dataMigrateTracker.LastModifiedBy,
                                        });
                                    }
                                }
                                if (isChannelPresent)
                                {
                                    var cpName = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.ChannelPartnerName]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(cpName) && !(exstingChannelPartners?.Contains(cpName.ToLower().Trim()) ?? false) && !newChannels.Select(i => i.FirmName).Contains(cpName))
                                    {
                                        newChannels.Add(new()
                                        {
                                            FirmName = cpName.Trim(),
                                            CreatedBy = dataMigrateTracker.CreatedBy,
                                            LastModifiedBy = dataMigrateTracker.LastModifiedBy,
                                        });
                                    }
                                }
                                if (isCampaignPresent)
                                {
                                    var campaignName = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.CampaignName]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(campaignName) && !(exstingCampaigns?.Contains(campaignName.ToLower()) ?? false) && !newCampaign.Select(i => i.Name).Contains(campaignName))
                                    {
                                        newCampaign.Add(new()
                                        {
                                            Name = campaignName,
                                            CreatedBy = dataMigrateTracker.CreatedBy,
                                            LastModifiedBy = dataMigrateTracker.LastModifiedBy,
                                        });
                                    }
                                }
                            });
                        }
                        if (newProperties.Any())
                        {
                            await _propertyRepo.AddRangeAsync(newProperties);
                            properties.AddRange(newProperties);
                        }
                        if (newProjects.Any())
                        {
                            await _newProjectRepo.AddRangeAsync(newProjects);
                            tempProjects.AddRange(newProjects);
                        }
                        if (newAgencies.Any())
                        {
                            await _agencyRepo.AddRangeAsync(newAgencies);
                            agencies.AddRange(newAgencies);
                        }
                        if (newChannels.Any())
                        {
                            await _cpRepository.AddRangeAsync(newChannels);
                            channelPartner.AddRange(newChannels);
                        }
                        if(newCampaign.Any())
                        {
                            await _campaignRepo.AddRangeAsync(newCampaign);
                            campaigns.AddRange(newCampaign);
                        }
                        #endregion

                        var unMappedColumns = dataTable.GetUnmappedColumnNames(dataMigrateTracker.MappedColumnsData ?? new());
                        var globalSettingInfoList = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);
                        List<Prospect> data = await dataTable.DataMigrateAsync(dataMigrateTracker.MappedColumnsData, unMappedColumns, propetyTypes, leadStatuses, users, sources, _propertyRepo, _newProjectRepo, 
                            agencies, dataMigrateTracker, globalSettingInfoList, channelPartner , campaigns,input.JsonData ?? string.Empty);
                        string callingCode = globalSettingInfoList?.Countries?.FirstOrDefault().DefaultCallingCode;
                        data = data.DistinctBy(i => i.ContactNo).ToList();

                        foreach (var prospect in data)
                        {
                            if (!string.IsNullOrWhiteSpace(prospect.ContactNo))
                            {
                                var contactNo = BulkUploadHelper.ConcatenatePhoneNumber(prospect.CountryCode, prospect.ContactNo, globalSettingInfoList);
                                if (string.IsNullOrWhiteSpace(contactNo))
                                {
                                    var invalidProspect = prospect.Adapt<InvalidProspect>();
                                    invalidProspect.Errors = "Invalid ContactNo";
                                    invalids.Add(invalidProspect);
                                }
                                prospect.ContactNo = contactNo;
                                if (!string.IsNullOrWhiteSpace(prospect.AlternateContactNo))
                                {
                                    var altcontactno = BulkUploadHelper.ConcatenatePhoneNumber(prospect.CountryCode, prospect.AlternateContactNo, globalSettingInfoList);
                                    prospect.AlternateContactNo = altcontactno;
                                }
                            }
                            else
                            {
                                var invalidProspect = prospect.Adapt<InvalidProspect>();
                                invalidProspect.Errors = "Invalid ContactNo";
                                invalids.Add(invalidProspect);
                            }
                        }


                        if (!(migrationType == DataMigrationType.CreateDuplicateData && duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded))
                        {
                            var contactNos = data.Select(i => i.ContactNo).ToList();

                            foreach (var contactNo in contactNos)
                            {
                                string mobileNumber = Regex.Replace(contactNo, "[^0-9]", "");
                                string defaultRegion = string.Empty;
                                PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
                                try
                                {
                                    if (contactNo.StartsWith("+") && contactNo.Length > 6 && contactNo.Length < 20)
                                    {
                                        PhoneNumber number = phoneUtil.Parse("+" + contactNo, null);
                                        defaultRegion = phoneUtil.GetRegionCodeForNumber(number);
                                        string countryCode = phoneUtil.GetCountryCodeForRegion(defaultRegion).ToString();
                                        countryCode = Regex.Replace(countryCode, "[^0-9]", "");
                                        if (mobileNumber.StartsWith(countryCode))
                                        {
                                            mobileNumber = mobileNumber.Substring(countryCode.Length);
                                        }
                                    }
                                }
                                catch
                                {
                                }
                                var leadsForNumber = await _prospectRepo.ListAsync(new CheckDuplicateProspectSpecs(mobileNumber), cancellationToken);
                                if (leadsForNumber != null)
                                {
                                    existingDatas.AddRange(leadsForNumber);
                                }

                            }
                        }
                        data.ForEach(d => d.SetData(dataMigrateTracker.MappedColumnsData, input.CurrentUserId));
                        var distinctLeadCount = data.Count();
                        Console.WriteLine($"handler() -> Total Distinct Lead: {distinctLeadCount}");
                        var existingContactNos = existingDatas.Select(i => i.ContactNo).ToList();
                        List<Prospect> datasToUpdate = new();
                        List<Prospect> existingLeadsToUpdate = new();
                        foreach (Prospect datas in data)
                        {
                            if (existingContactNos.Any(i => !string.IsNullOrWhiteSpace(datas.ContactNo)))
                            {
                                if (migrationType == DataMigrationType.UpdateMissingInformation)
                                {
                                    var invalidProspect = datas.Adapt<InvalidProspect>();
                                    invalidProspect.Errors = "Duplicate Data";
                                    var duplicateData = existingDatas.FirstOrDefault(i => !string.IsNullOrWhiteSpace(datas.ContactNo) && i.ContactNo.Contains(datas.ContactNo));
                                    if (duplicateData != null)
                                    {
                                        var user = users.FirstOrDefault(i => i.Id == duplicateData.AssignTo);
                                        invalidProspect.AssignTo = user != null ? user.FirstName + " " + user.LastName : string.Empty;
                                        datasToUpdate.Add(datas);
                                    }
                                    foreach (var leadToUpdate in datasToUpdate)
                                    {

                                        foreach (var property in leadToUpdate.GetType().GetProperties())
                                        {
                                            if (property.GetValue(leadToUpdate) == null)
                                            {
                                                var newValue = datas.GetType().GetProperty(property.Name)?.GetValue(datas);
                                                if (newValue != null)
                                                {
                                                    property.SetValue(leadToUpdate, newValue);
                                                }
                                            }
                                        }
                                    }
                                }
                                else if (migrationType == DataMigrationType.OverideExisitingProspectInformation)
                                {
                                    var invalidProspect = datas.Adapt<InvalidProspect>();
                                    invalidProspect.Errors = "Duplicate Data";
                                    var duplicateLead = existingDatas.FirstOrDefault(i => !string.IsNullOrWhiteSpace(datas.ContactNo) && i.ContactNo.Contains(datas.ContactNo));
                                    if (duplicateLead != null)
                                    {
                                        var user = users.FirstOrDefault(i => i.Id == duplicateLead.AssignTo);
                                        invalidProspect.AssignTo = user != null ? user.FirstName + " " + user.LastName : string.Empty;
                                        datasToUpdate.Add(datas);
                                    }
                                   /* datasToUpdate.Clear();
                                    datasToUpdate.Add(datas);*/
                                }
                                else
                                {
                                    var invalidProspect = datas.Adapt<InvalidProspect>();
                                    invalidProspect.Errors = "Duplicate Data";
                                    var duplicateProspect = existingDatas.FirstOrDefault(i => !string.IsNullOrWhiteSpace(datas.ContactNo) && i.ContactNo.Contains(datas.ContactNo));
                                    if (duplicateProspect != null)
                                    {
                                        var user = users.FirstOrDefault(i => i.Id == duplicateProspect.AssignTo);
                                        invalidProspect.AssignTo = user != null ? user.FirstName + " " + user.LastName : string.Empty;
                                        datasToUpdate.Add(datas);

                                        if (duplicateProspect.Status?.BaseId != null || duplicateProspect.Status?.BaseId != default)
                                        {
                                            invalidProspect.Status = leadStatuses?.FirstOrDefault(i => i.Id == datas.Status?.BaseId)?.Status ?? string.Empty;
                                            // invalidProspect.SubStatus = duplicateProspect.Status?.DisplayName ?? string.Empty;

                                        }
                                        else
                                        {
                                            invalidProspect.Status = duplicateProspect.Status?.DisplayName ?? string.Empty;
                                        }
                                        invalidProspect.Source = duplicateProspect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Source?.DisplayName?.ToString() ?? duplicateProspect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                                        invalidProspect.SubSource = duplicateProspect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? duplicateProspect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                                        invalidProspect.Created = duplicateProspect.CreatedOn.Date;
                                        invalids.Add(invalidProspect);
                                    }
                                }
                            }
                        };
                        data.RemoveAll(i => invalids.Select(i => i.ContactNo).Contains(i.ContactNo));
                        data.RemoveAll(i => existingContactNos.Select(i => i).Contains(i.ContactNo));
                        dataMigrateTracker.Status = UploadStatus.InProgress;
                        dataMigrateTracker.TotalCount = totalRows;
                        dataMigrateTracker.DistinctProspectCount = distinctLeadCount;
                        dataMigrateTracker.LastModifiedBy = input.CurrentUserId;
                        dataMigrateTracker.CreatedBy = input.CurrentUserId;

                        if (invalids.Any())
                        {
                            dataMigrateTracker.DuplicateCount = invalids.Where(i => i.Errors == "Duplicate Data").Count();
                            dataMigrateTracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty").Count();
                            byte[] bytes = ProspectHelper.CreateExcelData(invalids).ToArray();
                            string fileName = $"InvalidProspect-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = $"{input.TenantId}/Prospects/Migrate";
                            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            dataMigrateTracker.InvalidProspectS3BucketKey = key;
                        }
                        if (existingLeadsToUpdate.Any())
                        {
                            dataMigrateTracker.ProspectUpdatedCount = existingLeadsToUpdate.Count();
                        }
                        await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);
                        Console.WriteLine($"handler() -> BulkLeadUploadTracker Updated Status: {dataMigrateTracker.Status} \n {JsonConvert.SerializeObject(dataMigrateTracker)}");
                        Lrb.Application.DataManagement.Web.Dtos.BulkMigrateBackgroundDto backgroundDto = new();
                        if (data.Count > 0)
                        {
                            int leadsPerchunk = data.Count > 5000 ? 5000 : data.Count;
                            var chunks = data.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Prospect>(i));
                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new Lrb.Application.DataManagement.Web.Dtos.BulkMigrateBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = dataMigrateTracker.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Prospects = new(chunk),
                                    Users = users.ToList(),
                                    MigrationType = migrationType
                                };
                                await ExecuteDBOperationsDataAsync(backgroundDto);
                                chunkIndex++;
                            }
                        }

                        if (datasToUpdate?.Any() ?? false && migrationType == DataMigrationType.UpdateMissingInformation || migrationType == DataMigrationType.OverideExisitingProspectInformation)
                        {
                            int leadsPerchunk = datasToUpdate.Count > 5000 ? 5000 : datasToUpdate.Count;
                            var chunks = datasToUpdate.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Prospect>(i));

                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;

                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new Lrb.Application.DataManagement.Web.Dtos.BulkMigrateBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = dataMigrateTracker.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Prospects = new(chunk),
                                    Users = users.ToList(),
                                    MigrationType = migrationType
                                };
                                if (backgroundDto.Prospects.Any())
                                {
                                    await UpdateDuplicateDataAsync(backgroundDto, tempProjects, properties);
                                    chunkIndex++;
                                }


                            }
                        }
                        if (existingLeadsToUpdate?.Any() ?? false && migrationType == DataMigrationType.UpdateMissingInformation || migrationType == DataMigrationType.OverideExisitingProspectInformation)
                        {
                            int leadsPerchunk = existingLeadsToUpdate.Count > 5000 ? 5000 : existingLeadsToUpdate.Count;
                            var chunks = existingLeadsToUpdate.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Prospect>(i));

                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;

                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new Lrb.Application.DataManagement.Web.Dtos.BulkMigrateBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = dataMigrateTracker.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Prospects = new(chunk),
                                    Users = users.ToList(),
                                    MigrationType = migrationType
                                };
                                if (backgroundDto.Prospects.Any())
                                {
                                    await UpdateDuplicateDataAsync(backgroundDto, tempProjects, properties);
                                    chunkIndex++;
                                }


                            }
                        }
                        dataMigrateTracker.Status = UploadStatus.Completed;
                        dataMigrateTracker.LastModifiedBy = input.CurrentUserId;
                        dataMigrateTracker.CreatedBy = input.CurrentUserId;
                        dataMigrateTracker.TotalUploadedCount = data.Count;
                        await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);
                        if (data.Any() || (datasToUpdate?.Any() ?? false) || (existingLeadsToUpdate?.Any() ?? false))
                        {
                            var tracker = await _dataMigrateTrackerRepo.GetByIdAsync(backgroundDto.TrackerId);
                            try
                            {
                                await SendNotificationsAsync(backgroundDto);
                            }
                            catch (Exception e)
                            {
                                Console.WriteLine($"ExecuteDBOperationsDataAsync() -> Exception(Notification): {JsonConvert.SerializeObject(e)}");
                                if (tracker != null)
                                {
                                    if (tracker.TotalUploadedCount == (tracker.DistinctProspectCount - tracker.InvalidCount - tracker.DuplicateCount))
                                    {
                                        tracker.Status = UploadStatus.Completed;
                                    }
                                    else
                                    {
                                        tracker.Status = UploadStatus.Failed;
                                        tracker.Message = e?.InnerException?.Message ?? e?.Message;
                                    }
                                    tracker.LastModifiedBy = backgroundDto.CurrentUserId;
                                    tracker.CreatedBy = backgroundDto.CurrentUserId;
                                    await _dataMigrateTrackerRepo.UpdateAsync(tracker);
                                    var error = new LrbError()
                                    {
                                        ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                        ErrorSource = e?.Source,
                                        StackTrace = e?.StackTrace,
                                        InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),

                                        ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> ExecuteDBOperationsDataAsync() -> SendNotificationsAsync()",
                                    };
                                    await _leadRepositoryAsync.AddErrorAsync(error);
                                }
                            }
                        }
                    }

                    catch (Exception ex)
                    {
                        dataMigrateTracker = await _dataMigrateTrackerRepo.GetByIdAsync(dataMigrateTracker.Id);
                        if (dataMigrateTracker != null)
                        {
                            dataMigrateTracker.Status = UploadStatus.Failed;
                            dataMigrateTracker.Message = ex.Message;
                            dataMigrateTracker.LastModifiedBy = input.CurrentUserId;
                            dataMigrateTracker.CreatedBy = input.CurrentUserId;
                            await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);
                        }
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkDatadataUploadTrackerUsingEPPlus -> LeadHandler()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                        throw;
                    }
                }
                else
                {
                    Console.WriteLine($"handler() -> tracker not found by the Id : {input.TrackerId}");
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> LeadHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }

        }
        private async Task SendNotificationsAsync(Lrb.Application.DataManagement.Web.Dtos.BulkMigrateBackgroundDto dto)
        {

            var userIdsWithNoOfLeadsAssigned = dto.Prospects.Where(i => i.AssignTo != Guid.Empty).GroupBy(i => i.AssignTo).ToDictionary(i => i.Key, j => j.Count());
            if (userIdsWithNoOfLeadsAssigned.Count() > 0)
            {
                foreach (var item in userIdsWithNoOfLeadsAssigned)
                {
                    try
                    {
                        var userDetails = dto.Users?.FirstOrDefault(i => i.Id == item.Key);
                        if (userDetails != null)
                        {
                            if (userDetails.Id != dto.CurrentUserId)
                            {
                                var lead = dto.Prospects.FirstOrDefault(i => i.AssignTo == item.Key);
                                if (item.Value == 1)
                                {
                                    Console.WriteLine($"BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() -> Event: {Lrb.Domain.Enums.Event.LeadAssignment}, lead: {JsonConvert.SerializeObject(lead, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, UserDetails: {JsonConvert.SerializeObject(userDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, NoOfLeads: {item.Value}");
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.LeadAssignment, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, null, item.Value, dto.CurrentUserId);
                                }
                                else
                                {
                                    Console.WriteLine($"BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() -> Event: {Lrb.Domain.Enums.Event.MultipleLeadAssignment}, lead: {JsonConvert.SerializeObject(lead, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, UserDetails: {JsonConvert.SerializeObject(userDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, NoOfLeads: {item.Value}");
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.MultipleLeadAssignment, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, null, item.Value, dto.CurrentUserId);
                                }
                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> SendNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
        }
        private async Task ExecuteDBOperationsDataAsync(Lrb.Application.DataManagement.Web.Dtos.BulkMigrateBackgroundDto dto)
        {
            if (!(dto.Prospects?.Any() ?? false))
            {
                return;
            }
            DataMigrateTracker? tracker = new();
            tracker = await _dataMigrateTrackerRepo.GetByIdAsync(dto.TrackerId);
            try
            {
                if (dto.MigrationType == DataMigrationType.CreateDuplicateData)
                {
                    foreach (var data in dto.Prospects)
                    {
                        var parentProspect = await _prospectRepo.FirstOrDefaultAsync(new GetRootDataSpec(data.ContactNo ?? string.Empty));
                        if (parentProspect != null)
                        {
                            try
                            {
                                await _prospectRepo.UpdateAsync(parentProspect);
                            }
                            catch (Exception e)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                    ErrorSource = e?.Source,
                                    StackTrace = e?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),

                                    ErrorModule = "CreateBulkDatadataUploadTrackerUsingEPPlus -> ExecuteDBOperationsDataAsync() -> SendNotificationsAsync()",
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    }
                }
                DateTime[] createdOnStrings = new DateTime[dto.Prospects.Count];
                for (int i = 0; i < dto.Prospects.Count; i++)
                {
                    createdOnStrings[i] = dto.Prospects[i].CreatedOn.ConvertAndSetKindAsUtc(); // Convert to UTC
                }
                var datas = await _prospectRepo.AddRangeAsync(dto.Prospects);
                List<Prospect> prospectList = datas.ToList();
                for (int i = 0; i < prospectList.Count; i++)
                {
                    prospectList[i].CreatedOn = createdOnStrings[i];
                }
                var addresses = ExtractAddressFromProspects(datas.ToList());
                await AddBulkLocations(addresses);
                List<ProspectHistory> prospectHistories = new();
                var prospectVM = datas.Adapt<List<ViewProspectDto>>();
                var sources = await _prospectSourceRepo.ListAsync();
                var statuses = await _prospectStatusRepo.ListAsync();
                var propertyTypes = await _propertyTypeRepo.ListAsync();
                foreach (var vm in prospectVM)
                {
                    await ProspectHistoryHelper.SetUserViewForProspect(vm, _userService, dto.CancellationToken);
                    var histories = await ProspectHistoryHelper.CreateProspectHistoryForVM(vm, null, dto.CurrentUserId, 1, statuses, propertyTypes, sources, _userService, dto.CancellationToken);
                    await _prospectHistoryRepo.AddRangeAsync(histories);
                }
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Prospects.Count;
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _dataMigrateTrackerRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsDataAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker != null)
                {
                    if (tracker.TotalUploadedCount == (tracker.DistinctProspectCount - tracker.InvalidCount - tracker.DuplicateCount))
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    else
                    {
                        tracker.Status = UploadStatus.Failed;
                        tracker.Message = e?.InnerException?.Message ?? e?.Message;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _dataMigrateTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkDatadataUploadTrackerUsingEPPlus -> ExecuteDBOperationsDataAsync()",
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        private async Task UpdateDuplicateDataAsync(Lrb.Application.DataManagement.Web.Dtos.BulkMigrateBackgroundDto dto, List<Project> projects, List<Property> properties)
        {
            List<Prospect> prospectsToUpdate = new();
            DataMigrateTracker? tracker = await _dataMigrateTrackerRepo.GetByIdAsync(dto.TrackerId);
            try
            {
                if (dto.Prospects?.Any() ?? false)
                {
                    foreach (var data in dto.Prospects)
                    {
                        var duplicateDataToUpdate = await _prospectRepo.ListAsync(new GetDuplicateProspectByContactNoSpecs(data.ContactNo ?? string.Empty));
                        // var updatedLeads = MapAllObjects(data, duplicateDataToUpdate, projects, properties);
                        if (tracker.MigrationType == DataMigrationType.UpdateMissingInformation)
                         {
                          var updatedLeads = MapAllObjectsForUpdateMissingInformation(data, duplicateDataToUpdate, projects, properties);
                         prospectsToUpdate.AddRange(updatedLeads);
                        }
                        if(tracker.MigrationType == DataMigrationType.OverideExisitingProspectInformation)
                        {
                            var updatedLeads = MapAllObjects(data, duplicateDataToUpdate, projects, properties);
                            prospectsToUpdate.AddRange(updatedLeads);
                        }
                    }
                    try
                    {
                        await _prospectRepo.UpdateRangeAsync(prospectsToUpdate);
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine($"ExecuteDBOperationsDataAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                        if (tracker.TotalUploadedCount == (tracker.DistinctProspectCount - tracker.InvalidCount - tracker.DuplicateCount))
                        {
                            tracker.Status = UploadStatus.Completed;
                        }
                        else
                        {
                            tracker.Status = UploadStatus.Failed;
                            tracker.Message = e?.InnerException?.Message ?? e?.Message;
                        }
                        tracker.LastModifiedBy = dto.CurrentUserId;
                        tracker.CreatedBy = dto.CurrentUserId;
                        await _dataMigrateTrackerRepo.UpdateAsync(tracker);
                        var error = new LrbError()
                        {
                            ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                            ErrorSource = e?.Source,
                            StackTrace = e?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkDatadataUploadTrackerUsingEPPlus -> ExecuteDBOperationsDataAsync()",
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    var prospectDtos = prospectsToUpdate.Adapt<List<ViewProspectDto>>();
                    dto.ProspectDto = prospectDtos;
                    List<ProspectHistory> prospectHistories = new();

                    if (tracker != null)
                    {
                        tracker.TotalUploadedCount += dto.Prospects.Count;
                        tracker.LastModifiedBy = dto.CurrentUserId;
                        tracker.CreatedBy = dto.CurrentUserId;
                        await _dataMigrateTrackerRepo.UpdateAsync(tracker);
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsDataAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == (tracker.DistinctProspectCount - tracker.InvalidCount - tracker.DuplicateCount))
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _dataMigrateTrackerRepo.UpdateAsync(tracker);
            }
        }

        public List<Prospect> MapAllObjectsForUpdateMissingInformation(Prospect src, List<Prospect> destProspect, List<Project> projects, List<Property> properties)
        {
            foreach (var dest in destProspect)
            {
                MappingMethodForUpdateMissingInfodata(src, dest, projects, properties);
            }
            return destProspect;
        }


        public static void MappingMethodForUpdateMissingInfodata(Prospect src, Prospect dest, List<Project> projects, List<Property> properties)
        {
            if (!dest.Enquiries.Any())
            {
                dest.Enquiries = new List<ProspectEnquiry>();
            }
            try
            {
                if (src.Projects?.Any() ?? false)
                {
                    dest.Projects = new List<Project>() { projects.FirstOrDefault(i => i.Name == src.Projects?.FirstOrDefault()?.Name) ?? new() };
                }
                if (src.Properties?.Any() ?? false)
                {
                    dest.Properties = new List<Property>() { properties.FirstOrDefault(i => i.Title == src.Properties?.FirstOrDefault()?.Title) ?? new() };
                }
                if (dest.Email == null || dest.Email == string.Empty)
                { dest.Email = string.IsNullOrEmpty(src.Email) ? dest.Email : src.Email; }
                if (dest.Notes == null) { dest.Notes = string.IsNullOrEmpty(src.Notes) ? dest.Notes : src.Notes; }
                if (string.IsNullOrEmpty(dest.AlternateContactNo)) 
                { 
                    dest.AlternateContactNo = string.IsNullOrEmpty(src.AlternateContactNo) ? dest.AlternateContactNo : src.AlternateContactNo;
                }
                if (dest.AssignTo == null || dest.AssignTo == Guid.Empty) 
                { 
                    dest.AssignTo = (src.AssignTo != default && src.AssignTo != null) ? src.AssignTo : dest.AssignTo;
                }
                if (dest.AssignedFrom == null || dest.AssignedFrom == Guid.Empty) 
                { 
                    dest.AssignedFrom = (src.AssignedFrom != default && src.AssignedFrom != null) ? src.AssignedFrom : dest.AssignedFrom; 
                }
                //if (dest.AgencyName == null) { dest.AgencyName = string.IsNullOrEmpty(src.AgencyName) ? dest.AgencyName : src.AgencyName; }
                if (dest.Agencies == null || dest.Agencies.Count == 0) { dest.Agencies = src.Agencies ?? dest.Agencies; }
                if (dest.CreatedBy == null) { dest.CreatedBy = src.CreatedBy; }
                if (dest.ScheduleDate == null) { dest.ScheduleDate = src.ScheduleDate; }
                if (string.IsNullOrEmpty(dest.Notes)) { dest.Notes = src.Notes; }
                if(dest.Status == null) { dest.Status = src.Status; }
                if (dest.LastModifiedBy == null) { dest.LastModifiedBy = src.LastModifiedBy; }
                if (dest.Enquiries.FirstOrDefault().PropertyType == null) 
                { 
                    dest.Enquiries.FirstOrDefault().PropertyType = src.Enquiries?.FirstOrDefault()?.PropertyType != default ? src.Enquiries?.FirstOrDefault()?.PropertyType : dest.Enquiries[0].PropertyType;
                }
                if (dest.Enquiries.FirstOrDefault().PropertyTypes == null)
                {
                    dest.Enquiries.FirstOrDefault().PropertyTypes = src.Enquiries?.FirstOrDefault()?.PropertyTypes != default ? src.Enquiries?.FirstOrDefault()?.PropertyTypes : dest.Enquiries[0].PropertyTypes;
                }
                if (dest.Enquiries.FirstOrDefault().BHKs == null || dest.Enquiries.FirstOrDefault().BHKs.Count == 0) 
                {
                    dest.Enquiries.FirstOrDefault().BHKs = src.Enquiries?.FirstOrDefault()?.BHKs != default ? src.Enquiries?.FirstOrDefault()?.BHKs : dest.Enquiries[0].BHKs;
                }
                // dest.Enquiries.FirstOrDefault().EnquiredFor = src.Enquiries?.FirstOrDefault()?.EnquiredFor != default ? src.Enquiries.FirstOrDefault().EnquiredFor : dest.Enquiries[0].EnquiredFor;
                if (dest.Enquiries.FirstOrDefault().EnquiryTypes == null || dest.Enquiries.FirstOrDefault().EnquiryTypes.Count == 0)
                { 
                    dest.Enquiries.FirstOrDefault().EnquiryTypes = src.Enquiries?.FirstOrDefault()?.EnquiryTypes != default ? src.Enquiries?.FirstOrDefault()?.EnquiryTypes : dest.Enquiries[0].EnquiryTypes; 
                }
               // if (dest.Enquiries.FirstOrDefault().SaleType == null) { dest.Enquiries.FirstOrDefault().SaleType = dest.Enquiries[0].SaleType; }
                if (string.IsNullOrEmpty(dest.Enquiries.FirstOrDefault().SubSource))
                { 
                    dest.Enquiries.FirstOrDefault().SubSource = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.SubSource) ? dest.Enquiries[0].SubSource : src.Enquiries?.FirstOrDefault()?.SubSource; 
                }
                if (dest.Enquiries.FirstOrDefault().Source == null) 
                {
                    dest.Enquiries.FirstOrDefault().Source = src.Enquiries?.FirstOrDefault()?.Source != default ? src.Enquiries.FirstOrDefault().Source : dest.Enquiries[0].Source; 
                }
                if (dest.Enquiries.FirstOrDefault().LowerBudget == null || dest.Enquiries.FirstOrDefault().LowerBudget == 0)
                { 
                    dest.Enquiries.FirstOrDefault().LowerBudget = (src.Enquiries?.FirstOrDefault()?.LowerBudget != default && src.Enquiries?.FirstOrDefault()?.LowerBudget != null) ? src.Enquiries?.FirstOrDefault()?.LowerBudget : dest.Enquiries[0].LowerBudget;
                }
                if (dest.Enquiries.FirstOrDefault().UpperBudget == null || dest.Enquiries.FirstOrDefault().UpperBudget == 0) 
                { 
                    dest.Enquiries.FirstOrDefault().UpperBudget = (src.Enquiries?.FirstOrDefault()?.UpperBudget != default && src.Enquiries?.FirstOrDefault()?.UpperBudget != null) ? src.Enquiries?.FirstOrDefault()?.UpperBudget : dest.Enquiries[0].UpperBudget;
                }
                //if (dest.Enquiries.FirstOrDefault().BHKType == null || dest.Enquiries.FirstOrDefault().BHKType ==0) { dest.Enquiries.FirstOrDefault().BHKType = src.Enquiries?.FirstOrDefault()?.BHKType != default ? src.Enquiries.FirstOrDefault().BHKType : dest.Enquiries[0].BHKType; }
                if (dest.Enquiries.FirstOrDefault().BHKTypes == null || dest.Enquiries.FirstOrDefault().BHKTypes.Count == 0) 
                { 
                    dest.Enquiries.FirstOrDefault().BHKTypes = src.Enquiries?.FirstOrDefault()?.BHKTypes != default ? src.Enquiries?.FirstOrDefault()?.BHKTypes : dest.Enquiries[0].BHKTypes; 
                }
                try
                {
                    dest.Enquiries.FirstOrDefault().Address.City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Address?.City) ? dest.Enquiries[0]?.Address?.City : src.Enquiries?.FirstOrDefault()?.Address?.City;
                    dest.Enquiries.FirstOrDefault().Address.State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Address?.State) ? dest.Enquiries[0]?.Address?.State : src.Enquiries?.FirstOrDefault()?.Address?.State;
                    dest.Enquiries.FirstOrDefault().Address.Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Address?.Country) ? dest.Enquiries[0]?.Address?.Country : src.Enquiries?.FirstOrDefault()?.Address?.Country;
                }
                catch
                {

                }
                if (dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubLocality == null || dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubLocality == string.Empty)
                { 
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubLocality = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.SubLocality : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality; 
                }
                if (dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City == null || dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City == string.Empty)
                {
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.City : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City; 
                }
                if (dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State == null || dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State == string.Empty)
                { 
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.State : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State;
                }
                if (dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country == null || dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country == string.Empty)
                { 
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.Country : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country;
                }

            }
            catch
            {

            }
        }

        private List<Prospect> MapAllObjects(Prospect src, List<Prospect> destProspects, List<Project> projects, List<Property> properties)
        {
            foreach (var dest in destProspects)
            {
                if (!dest.Enquiries.Any())
                {
                    dest.Enquiries = new List<ProspectEnquiry>();
                }
                try
                {
                    if (src.Projects?.Any() ?? false)
                    {
                        dest.Projects = new List<Project>() { projects.FirstOrDefault(i => i.Name == src.Projects?.FirstOrDefault()?.Name) ?? new() };
                    }
                    if (src.Properties?.Any() ?? false)
                    {
                        dest.Properties = new List<Property>() { properties.FirstOrDefault(i => i.Title == src.Properties?.FirstOrDefault()?.Title) ?? new() };
                    }
                    dest.Email = string.IsNullOrEmpty(src.Email) ? dest.Email : src.Email;
                    dest.Notes = string.IsNullOrEmpty(src.Notes) ? dest.Notes : src.Notes;
                    dest.AlternateContactNo = string.IsNullOrEmpty(src.AlternateContactNo) ? dest.AlternateContactNo : src.AlternateContactNo;
                    dest.AssignTo = (src.AssignTo != default && src.AssignTo != null) ? src.AssignTo : dest.AssignTo;
                    //dest.AssignedFrom = (src.AssignedFrom != default && src.AssignedFrom != null) ? src.AssignedFrom : dest.AssignedFrom;
                    //dest.AgencyName = string.IsNullOrEmpty(src.AgencyName) ? dest.AgencyName : src.AgencyName;
                    dest.Agencies =(src.Agencies==null || src.Agencies.Count==0)  ? dest.Agencies : src.Agencies;
                    dest.CreatedBy = (src.CreatedBy == null || src.CreatedBy == Guid.Empty) ? dest.CreatedBy : src.CreatedBy;
                    // dest.CustomFlags = (src.CustomFlags == null || src.CustomFlags.Count == 0) ? dest.CustomFlags : src.CustomFlags;
                    dest.LastModifiedBy = src.LastModifiedBy;
                    dest.CreatedOn = src.CreatedOn;
                   // dest.CreatedBy = src.CreatedBy;
                    dest.Notes = (string.IsNullOrEmpty(src.Notes)) ? dest.Notes : src.Notes;
                    dest.ScheduleDate = (src.ScheduleDate == null) ? dest.ScheduleDate : src.ScheduleDate;
                    dest.Status = src.Status ?? dest.Status;
                    dest.Enquiries.FirstOrDefault().BHKs = src.Enquiries?.FirstOrDefault()?.BHKs != default ? src.Enquiries?.FirstOrDefault()?.BHKs : dest.Enquiries[0].BHKs;
                    dest.Enquiries.FirstOrDefault().EnquiryTypes = src.Enquiries?.FirstOrDefault()?.EnquiryTypes != default ? src.Enquiries?.FirstOrDefault()?.EnquiryTypes : dest.Enquiries[0].EnquiryTypes;
                    //dest.Enquiries.FirstOrDefault().SaleType = dest.Enquiries[0].SaleType;
                    dest.Enquiries.FirstOrDefault().SubSource = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.SubSource) ? dest.Enquiries[0].SubSource : src.Enquiries?.FirstOrDefault()?.SubSource;
                    dest.Enquiries.FirstOrDefault().Source = src.Enquiries?.FirstOrDefault().Source != default ? src.Enquiries.FirstOrDefault().Source : dest.Enquiries[0].Source;
                    dest.Enquiries.FirstOrDefault().LowerBudget = (src.Enquiries?.FirstOrDefault()?.LowerBudget != default && src.Enquiries?.FirstOrDefault()?.LowerBudget != null) ? src.Enquiries?.FirstOrDefault()?.LowerBudget : dest.Enquiries[0].LowerBudget;
                    dest.Enquiries.FirstOrDefault().UpperBudget = (src.Enquiries?.FirstOrDefault()?.UpperBudget != default && src.Enquiries?.FirstOrDefault()?.UpperBudget != null) ? src.Enquiries?.FirstOrDefault()?.UpperBudget : dest.Enquiries[0].UpperBudget;
                    // dest.Enquiries.FirstOrDefault().BHKType = src.Enquiries?.FirstOrDefault()?.BHKType != default ? src.Enquiries.FirstOrDefault().BHKType : dest.Enquiries[0].BHKType;
                    dest.Enquiries.FirstOrDefault().BHKTypes = src.Enquiries?.FirstOrDefault()?.BHKTypes != default ? src.Enquiries?.FirstOrDefault()?.BHKTypes : dest.Enquiries[0].BHKTypes;
                    dest.Enquiries.FirstOrDefault().PropertyType = src.Enquiries?.FirstOrDefault()?.PropertyType != default ? src.Enquiries?.FirstOrDefault()?.PropertyType : dest.Enquiries[0].PropertyType;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubLocality = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.SubLocality : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.City : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.State : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.Country : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country;
                    dest.Enquiries.FirstOrDefault().PropertyTypes = src.Enquiries?.FirstOrDefault()?.PropertyTypes != default ? src.Enquiries?.FirstOrDefault()?.PropertyTypes: dest.Enquiries[0].PropertyTypes;

                    // dest.ChannelPartnerName = string.IsNullOrEmpty(src.ChannelPartnerName) ? dest.ChannelPartnerName : src.ChannelPartnerName;
                }
                catch
                {

                }
            }
            return destProspects;
        }
       
    }
}

