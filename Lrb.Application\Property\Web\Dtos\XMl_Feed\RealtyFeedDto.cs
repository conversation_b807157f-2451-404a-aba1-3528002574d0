using System.Xml.Serialization;

namespace Lrb.Application.Property.Web.Dtos.XMl_Feed
{
    [XmlRoot("realty-feed")]
    public class RealtyFeedDto
    {
        [XmlElement("generation-date")]
        public string? GenerationDate { get; set; }

        [XmlElement("offers")]
        public List<RealtyOfferDto>? Offers { get; set; }
    }

    public class RealtyOfferDto
    {
        [XmlElement("complex-id")]
        public string? ComplexId { get; set; }

        [XmlElement("type")]
        public string? Type { get; set; }

        [XmlElement("logo")]
        public string? Logo { get; set; }

        [XmlElement("photo")]
        public string? Photo { get; set; }

        [XmlElement("title")]
        public MultiLanguageTextDto? Title { get; set; }

        [XmlElement("description")]
        public MultiLanguageTextDto? Description { get; set; }

        [XmlElement("status")]
        public MultiLanguageTextDto? Status { get; set; }

        [XmlElement("construction_start_at")]
        public string? ConstructionStartAt { get; set; }

        [XmlElement("construction_progress")]
        public string? ConstructionProgress { get; set; }

        [XmlElement("planned_completion_at")]
        public string? PlannedCompletionAt { get; set; }

        [XmlElement("predicted_completion_at")]
        public string? PredictedCompletionAt { get; set; }

        [XmlElement("amenities")]
        public AmenitiesDto? Amenities { get; set; }

        [XmlElement("developer")]
        public DeveloperDto? Developer { get; set; }

        [XmlElement("districts")]
        public DistrictsDto? Districts { get; set; }

        [XmlElement("address")]
        public string? Address { get; set; }

        [XmlElement("latitude")]
        public string? Latitude { get; set; }

        [XmlElement("longitude")]
        public string? Longitude { get; set; }

        [XmlElement("album")]
        public AlbumDto? Album { get; set; }

        [XmlElement("buildings_count")]
        public string? BuildingsCount { get; set; }

        [XmlElement("for_sale_count")]
        public string? ForSaleCount { get; set; }

        [XmlElement("price")]
        public PriceDto? Price { get; set; }

        [XmlElement("br_prices")]
        public List<BedroomPriceDto>? BedroomPrices { get; set; }

        [XmlElement("updated_at")]
        public string? UpdatedAt { get; set; }

        [XmlElement("is_sold_out")]
        public string? IsSoldOut { get; set; }

        [XmlElement("payment_plans")]
        public PaymentPlansDto? PaymentPlans { get; set; }

        [XmlElement("sales_status")]
        public MultiLanguageTextDto? SalesStatus { get; set; }
    }

    public class MultiLanguageTextDto
    {
        [XmlElement("ru")]
        public string? Russian { get; set; }

        [XmlElement("en")]
        public string? English { get; set; }

        [XmlElement("ar")]
        public string? Arabic { get; set; }
    }

    public class AmenitiesDto
    {
        [XmlElement("amenity")]
        public List<MultiLanguageTextDto>? Amenity { get; set; }
    }

    public class DeveloperDto
    {
        [XmlElement("title")]
        public MultiLanguageTextDto? Title { get; set; }

        [XmlElement("logo")]
        public string? Logo { get; set; }
    }

    public class DistrictsDto
    {
        [XmlElement("district")]
        public List<string>? District { get; set; }
    }

    public class AlbumDto
    {
        [XmlElement("image")]
        public List<string>? Images { get; set; }
    }

    public class PriceDto
    {
        [XmlElement("min")]
        public string? Min { get; set; }

        [XmlElement("max")]
        public string? Max { get; set; }

        [XmlElement("currency")]
        public string? Currency { get; set; }
    }

    public class BedroomPriceDto
    {
        [XmlElement("key")]
        public string? Key { get; set; }
            
        [XmlElement("count")]
        public string? Count { get; set; }

        [XmlElement("min_price")]
        public string? MinPrice { get; set; }

        [XmlElement("max_price")]
        public string? MaxPrice { get; set; }

        [XmlElement("min_price_m2")]
        public string? MinPriceM2 { get; set; }

        [XmlElement("max_price_m2")]
        public string? MaxPriceM2 { get; set; }

        [XmlElement("currency")]
        public string? Currency { get; set; }

        [XmlElement("min_area")]
        public AreaDto? MinArea { get; set; }

        [XmlElement("max_area")]
        public AreaDto? MaxArea { get; set; }
    }

    public class AreaDto
    {
        [XmlElement("m2")]
        public string? M2 { get; set; }

        [XmlElement("ft2")]
        public string? Ft2 { get; set; }
    }

    public class PaymentPlansDto
    {
        [XmlElement("payment_plan")]
        public List<PaymentPlanDto>? PaymentPlan { get; set; }
    }

    public class PaymentPlanDto
    {
        [XmlElement("id")]
        public string? Id { get; set; }

        [XmlElement("title")]
        public MultiLanguageTextDto? Title { get; set; }

        [XmlElement("on_booking_percent")]
        public string? OnBookingPercent { get; set; }

        [XmlElement("on_booking_fix")]
        public string? OnBookingFix { get; set; }

        [XmlElement("on_construction_percent")]
        public string? OnConstructionPercent { get; set; }

        [XmlElement("on_construction_fix")]
        public string? OnConstructionFix { get; set; }

        [XmlElement("on_handover_percent")]
        public string? OnHandoverPercent { get; set; }

        [XmlElement("on_handover_fix")]
        public string? OnHandoverFix { get; set; }

        [XmlElement("post_handover_percent")]
        public string? PostHandoverPercent { get; set; }

        [XmlElement("post_handover_fix")]
        public string? PostHandoverFix { get; set; }

        [XmlElement("roi_percent")]
        public string? RoiPercent { get; set; }

        [XmlElement("roi_fix")]
        public string? RoiFix { get; set; }

        [XmlElement("currency")]
        public string? Currency { get; set; }
    }
}
