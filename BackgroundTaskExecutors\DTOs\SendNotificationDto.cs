﻿using Lrb.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BackgroundTaskExecutors.DTOs
{
    public class SendNotificationDto
    {
        public object Entity { get; set; }
        public Event @event { get; set; }
        public Guid AssignTo { get; set; }
        public string? UserName { get; set; }
        public List<string>? Topics { get; set; }
        public int? NoOfEntities { get; set; }
        public Guid? CurrentUserIdPm { get; set; }
        public List<Guid>? UserIds { get; set; }
        public LeadSource? LeadSource { get; set; }
        public Guid CurrentUserId { get; set; }
        public string TenantId {  get; set; }
    }
}
