﻿using Lrb.Application.Email.Web;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.Reports.Web;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Shared.Extensions;
using Mapster;
using Newtonsoft.Json;
using StackExchange.Redis;
using static Lrb.Application.Lead.Utils.LeadExcelHelper;
using Lrb.Application.GlobalSettings.Web.Dto;

namespace ExcelUpload
{
    partial class FunctionEntryPoint
    {
        public async Task ExportAgencyReportByLeadStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForAgencyReportByStatusRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForAgencyReportByStatusRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }

                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ReportPermission != null)
                        {
                            switch (request.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<ViewAgencyReportDto> newReportByAgencyNameDtos = new List<ViewAgencyReportDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        request.FromDateForAgency = request.FromDateForAgency.HasValue ? request.FromDateForAgency.Value.ConvertFromDateToUtc() : null;
                        request.ToDateForAgency = request.ToDateForAgency.HasValue ? request.ToDateForAgency.Value.ConvertFromDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<AgencyReportDto>("LeadratBlack", "Lead_GetAgencyReportByStatus", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            userids = teamUserIds,
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            tenantid = tenantId,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            fromdateformeetingorvisit = request?.FromDateForAgency,
                            todateformeetingorvisit = request?.ToDateForAgency
                        }, 300))?.ToList() ?? new List<AgencyReportDto>();
                        var data = res.GroupBy(i => new Tuple<Guid, string>(i.AgencyId, i?.Name ?? string.Empty)).ToDictionary(i => i.Key, j => j.ToList());
                        foreach (var item in data)
                        {
                            var value = new ViewAgencyReportDto();
                            value.Name = item.Key.Item2;
                            value.AllCount = item.Value?.Select(j => j.AllCount)?.Sum(i => i) ?? 0;
                            value.ActiveCount = item.Value?.Select(j => j.ActiveCount)?.Sum(i => i) ?? 0;
                            value.MeetingDoneCount = item.Value?.Select(j => j.MeetingDoneCount)?.Sum(i => i) ?? 0;
                            value.MeetingDoneUniqueCount = item.Value?.Select(j => j.MeetingDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.MeetingNotDoneCount = item.Value?.Select(j => j.MeetingNotDoneCount)?.Sum(i => i) ?? 0;
                            value.MeetingNotDoneUniqueCount = item.Value?.Select(j => j.MeetingNotDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitDoneCount = item.Value?.Select(j => j.SiteVisitDoneCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitDoneUniqueCount = item.Value?.Select(j => j.SiteVisitDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitNotDoneCount = item.Value?.Select(j => j.SiteVisitNotDoneCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitNotDoneUniqueCount = item.Value?.Select(j => j.SiteVisitNotDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.OverdueCount = item.Value?.Select(j => j.OverdueCount)?.Sum(i => i) ?? 0;
                            var allItems = item.Value?.Where(i => (i.StatusId != null))?.ToList();
                            if (allItems?.Any() ?? false)
                            {
                                value.Status = ReportHelper.MapCustomStatusAndChildStatus(allItems.Adapt<List<ViewStatusDto>>());
                            }
                            newReportByAgencyNameDtos.Add(value);
                        }
                        var leadStatus = await _customMastereadStatus.ListAsync(cancellationToken);
                        var headers = ExportDataHelper.GetLeadHeaders<FormattedAgencyReportDto>(leadStatus, false);
                        var dtos = newReportByAgencyNameDtos.Adapt<List<FormattedAgencyReportDto>>();
                        var fileBytes = LeadExcelGeneration<FormattedAgencyReportDto>.GenerateExcel(dtos, "Agency Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Reports/{tenantId ?? "Default"}", $"Export_Lead_Agency_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email);
                        isSent = true;
                        tracker.Count = dtos.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Lead_Agency_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadStatusReportByAgencyHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        public async Task ExportProjectReportByLeadStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForProjectReportByStatusRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForProjectReportByStatusRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }

                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ReportPermission != null)
                        {
                            switch (request.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<ViewProjectReportDto> newReportByProjects = new List<ViewProjectReportDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        request.FromDateForProject = request.FromDateForProject.HasValue ? request.FromDateForProject.Value.ConvertFromDateToUtc() : null;
                        request.ToDateForProject = request.ToDateForProject.HasValue ? request.ToDateForProject.Value.ConvertFromDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<ProjectReportByStatusDto>("LeadratBlack", "Lead_ProjectReportByStatus", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            fromdateformeetingorvisit = request?.FromDateForProject,
                            todateformeetingorvisit = request?.ToDateForProject
                        }, 300)).ToList();
                        var data = res.GroupBy(i => new Tuple<Guid, string>(i.Id, i?.Name ?? string.Empty)).ToDictionary(i => i.Key, j => j.ToList());
                        foreach (var item in data)
                        {
                            var value = new ViewProjectReportDto();
                            value.Name = item.Key.Item2;
                            value.Id = item.Key.Item1;
                            value.AllCount = item.Value?.Select(j => j.AllCount)?.Sum(i => i) ?? 0;
                            value.ActiveCount = item.Value?.Select(j => j.ActiveCount)?.Sum(i => i) ?? 0;
                            value.MeetingDoneCount = item.Value?.Select(j => j.MeetingDoneCount)?.Sum(i => i) ?? 0;
                            value.MeetingDoneUniqueCount = item.Value?.Select(j => j.MeetingDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.MeetingNotDoneCount = item.Value?.Select(j => j.MeetingNotDoneCount)?.Sum(i => i) ?? 0;
                            value.MeetingNotDoneUniqueCount = item.Value?.Select(j => j.MeetingNotDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitDoneCount = item.Value?.Select(j => j.SiteVisitDoneCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitDoneUniqueCount = item.Value?.Select(j => j.SiteVisitDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitNotDoneCount = item.Value?.Select(j => j.SiteVisitNotDoneCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitNotDoneUniqueCount = item.Value?.Select(j => j.SiteVisitNotDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.OverdueCount = item.Value?.Select(j => j.OverdueCount)?.Sum(i => i) ?? 0;
                            var allItems = item.Value?.Where(i => (i.StatusId != null))?.ToList();
                            if (allItems?.Any() ?? false)
                            {
                                value.Status = ReportHelper.MapCustomStatusAndChildStatus(allItems.Adapt<List<ViewStatusDto>>());
                            }
                            newReportByProjects.Add(value);
                        }
                        var leadStatus = await _customMastereadStatus.ListAsync(cancellationToken);
                        var headers = ExportDataHelper.GetLeadHeaders<FormattedProjectReportDto>(leadStatus, false);
                        var dtos = newReportByProjects.Adapt<List<FormattedProjectReportDto>>();
                        var fileBytes = LeadExcelGeneration<FormattedProjectReportDto>.GenerateExcel(dtos, "Project Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Reports/{tenantId ?? "Default"}", $"Export_Lead_Project_Report_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email);
                        isSent = true;
                        tracker.Count = dtos.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Lead_Project_Report_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";

                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadStatusReportByAgencyHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        public async Task ExportSourceReportByLeadStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForSourceReportByStatusRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForSourceReportByStatusRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }

                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ReportPermission != null)
                        {
                            switch (request.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        if (!string.IsNullOrEmpty(request?.SearchText))
                        {
                            var result = ReportHelper.GetLeadSourceInfo(request.SearchText);
                            if (result?.IsValidInfo ?? false)
                            {
                                request?.Sources?.Add(result.LeadSource);
                                request.SearchText = null;
                            }
                        }
                        List<ViewSourceReportDto> newReportBySourceDtos = new List<ViewSourceReportDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        request.FromDateForSource = request.FromDateForSource.HasValue ? request.FromDateForSource.Value.ConvertFromDateToUtc() : null;
                        request.ToDateForSource = request.ToDateForSource.HasValue ? request.ToDateForSource.Value.ConvertToDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<SourceReportByStatusDto>("LeadratBlack", "Lead_SourceReportByStatus", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            userids = teamUserIds,
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            tenantid = tenantId,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            fromdateformeetingorvisit = request?.FromDateForSource,
                            todateformeetingorvisit = request?.ToDateForSource
                        }, 300))?.ToList() ?? new List<SourceReportByStatusDto>();
                        var data = res.GroupBy(i => i.Source).ToDictionary(i => i.Key, j => j.ToList());
                        foreach (var item in data)
                        {
                            var value = new ViewSourceReportDto();
                            value.Source = item.Key;
                            value.AllCount = item.Value?.Select(j => j.AllCount)?.Sum(i => i) ?? 0;
                            value.ActiveCount = item.Value?.Select(j => j.ActiveCount)?.Sum(i => i) ?? 0;
                            value.MeetingDoneCount = item.Value?.Select(j => j.MeetingDoneCount)?.Sum(i => i) ?? 0;
                            value.MeetingDoneUniqueCount = item.Value?.Select(j => j.MeetingDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.MeetingNotDoneCount = item.Value?.Select(j => j.MeetingNotDoneCount)?.Sum(i => i) ?? 0;
                            value.MeetingNotDoneUniqueCount = item.Value?.Select(j => j.MeetingNotDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitDoneCount = item.Value?.Select(j => j.SiteVisitDoneCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitDoneUniqueCount = item.Value?.Select(j => j.SiteVisitDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitNotDoneCount = item.Value?.Select(j => j.SiteVisitNotDoneCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitNotDoneUniqueCount = item.Value?.Select(j => j.SiteVisitNotDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.OverdueCount = item.Value?.Select(j => j.OverdueCount)?.Sum(i => i) ?? 0;
                            var allItems = item.Value?.Where(i => (i.StatusId != null))?.ToList();
                            if (allItems?.Any() ?? false)
                            {
                                value.Status = ReportHelper.MapCustomStatusAndChildStatus(allItems.Adapt<List<ViewStatusDto>>());
                            }
                            newReportBySourceDtos.Add(value);
                        }
                        var leadStatus = await _customMastereadStatus.ListAsync(cancellationToken);
                        var headers = ExportDataHelper.GetLeadHeaders<FormattedSourceReportDto>(leadStatus, false);
                        var dtos = newReportBySourceDtos.Adapt<List<FormattedSourceReportDto>>();
                        var fileBytes = LeadExcelGeneration<FormattedSourceReportDto>.GenerateExcel(dtos, "Source Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Reports/{tenantId ?? "Default"}", $"Export_Lead_Source_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email);
                        isSent = true;
                        tracker.Count = dtos.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Lead_Source_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadStatusReportByAgencyHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        public async Task ExportSubsourceReportByLeadStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForSubSourceReportByLeadStatusRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForSubSourceReportByLeadStatusRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }

                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ReportPermission != null)
                        {
                            switch (request.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<ViewSubSourceReportDto> newReportBySubSourceDtos = new List<ViewSubSourceReportDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        request.FromDateForSubSource = request.FromDateForSubSource.HasValue ? request.FromDateForSubSource.Value.ConvertFromDateToUtc() : null;
                        request.ToDateForSubSource = request.ToDateForSubSource.HasValue ? request.ToDateForSubSource.Value.ConvertToDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<SubSourceReportByStatusDto>("LeadratBlack", "Lead_SubSourceReportByStatus", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            userids = teamUserIds,
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            tenantid = tenantId,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            fromdateformeetingorvisit = request.FromDateForSubSource,
                            todateformeetingorvisit = request.ToDateForSubSource
                        }, 300))?.ToList() ?? new List<SubSourceReportByStatusDto>();
                        var data = res.Where(i => !string.IsNullOrWhiteSpace(i.Name)).GroupBy(i => i.Name ?? string.Empty).ToDictionary(i => i.Key, j => j.ToList());
                        foreach (var item in data)
                        {
                            var value = new ViewSubSourceReportDto();
                            value.Name = item.Key;
                            value.AllCount = item.Value?.Select(j => j.AllCount)?.Sum(i => i) ?? 0;
                            value.ActiveCount = item.Value?.Select(j => j.ActiveCount)?.Sum(i => i) ?? 0;
                            value.MeetingDoneCount = item.Value?.Select(j => j.MeetingDoneCount)?.Sum(i => i) ?? 0;
                            value.MeetingDoneUniqueCount = item.Value?.Select(j => j.MeetingDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.MeetingNotDoneCount = item.Value?.Select(j => j.MeetingNotDoneCount)?.Sum(i => i) ?? 0;
                            value.MeetingNotDoneUniqueCount = item.Value?.Select(j => j.MeetingNotDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitDoneCount = item.Value?.Select(j => j.SiteVisitDoneCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitDoneUniqueCount = item.Value?.Select(j => j.SiteVisitDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitNotDoneCount = item.Value?.Select(j => j.SiteVisitNotDoneCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitNotDoneUniqueCount = item.Value?.Select(j => j.SiteVisitNotDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.OverdueCount = item.Value?.Select(j => j.OverdueCount)?.Sum(i => i) ?? 0;
                            var allItems = item.Value?.Where(i => (i.StatusId != null))?.ToList();
                            if (allItems?.Any() ?? false)
                            {
                                value.Status = ReportHelper.MapCustomStatusAndChildStatus(allItems.Adapt<List<ViewStatusDto>>());
                            }
                            newReportBySubSourceDtos.Add(value);
                        }
                        var leadStatus = await _customMastereadStatus.ListAsync(cancellationToken);
                        var headers = ExportDataHelper.GetLeadHeaders<FormattedSubSourceReportDto>(leadStatus, false);
                        var dtos = newReportBySubSourceDtos.Adapt<List<FormattedSubSourceReportDto>>();
                        var fileBytes = LeadExcelGeneration<FormattedSubSourceReportDto>.GenerateExcel(dtos, "SubSource Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Reports/{tenantId ?? "Default"}", $"Export_Lead_Subsource_By_Status_Report_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email);
                        isSent = true;
                        tracker.Count = dtos.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Lead_Subsource_By_Status_Report_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadStatusReportByAgencyHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        public async Task ExportUserReportByLeadStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForStatusReportByUsersRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForStatusReportByUsersRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;
                        List<string>? defaultColumns = new() { "SlNo", "FirstName", "LastName" };
                        var selectedColumns = filtersDto.SelectedColumns.Select(i => i.Trim().Replace(" ", "")).ToList();
                        if (selectedColumns?.Any(i => i == "AllLeads") ?? false)
                        {
                            selectedColumns = selectedColumns.Select(i =>
                            {
                                if (i == "AllLeads")
                                {
                                    i = "All";
                                }
                                return i;
                            }).ToList();

                        }
                        if (selectedColumns?.Any(i => i == "ActiveLeads") ?? false)
                        {
                            selectedColumns = selectedColumns.Select(i =>
                            {
                                if (i == "ActiveLeads")
                                {
                                    i = "Active";
                                }
                                return i;
                            }).ToList();

                        }
                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }

                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ReportPermission != null)
                        {
                            switch (request.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else if (request?.IsWithGeneralManager ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithColumnNameAsync(request.UserIds, tenantId ?? string.Empty, "GeneralManager")).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }

                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<ViewUserReportDto> userDtos = new List<ViewUserReportDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserReportByStatusDto>("LeadratBlack", "Lead_GetLeadReportByUser", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = (request?.UserStatus ?? 0),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            shouldshowall = request?.ShouldShowAll
                        }, 300)).ToList();
                        var data = res.GroupBy(i => new Tuple<Guid, string, string, string, string>(i.Id, i?.FirstName ?? string.Empty, i?.LastName ?? string.Empty, i?.GeneralManager ?? string.Empty, i?.ReportingManager ?? string.Empty)).ToDictionary(i => i.Key, j => j.ToList());
                        foreach (var item in data)
                        {
                            float totalCount = item.Value?.Select(j => j.AllCount)?.Sum(i => i) ?? 0;
                            totalCount = totalCount == 0 ? 1 : totalCount;
                            var value = new ViewUserReportDto();
                            value.Id = item.Key.Item1;
                            value.FirstName = item.Key.Item2;
                            value.LastName = item.Key.Item3;
                            value.GeneralManager = item.Key.Item4;
                            value.ReportingManager = item.Key.Item5;
                            value.AllCount = item.Value?.Select(j => j.AllCount)?.Sum(i => i) ?? 0;
                            value.ActiveCount = item.Value?.Select(j => j.ActiveCount)?.Sum(i => i) ?? 0;
                            value.MeetingDoneCount = item.Value?.Select(j => j.MeetingDoneCount)?.Sum(i => i) ?? 0;
                            value.MeetingDoneUniqueCount = item.Value?.Select(j => j.MeetingDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.MeetingDoneUniqueCountPercentage = GetCountPercentage(item.Value?.Select(j => j.MeetingDoneUniqueCount)?.Sum(i => i) ?? 0, totalCount);
                            value.MeetingNotDoneCount = item.Value?.Select(j => j.MeetingNotDoneCount)?.Sum(i => i) ?? 0;
                            value.MeetingNotDoneUniqueCount = item.Value?.Select(j => j.MeetingNotDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.MeetingNotDoneUniqueCountPercentage = GetCountPercentage(item.Value?.Select(j => j.MeetingNotDoneUniqueCount)?.Sum(i => i) ?? 0, totalCount);
                            value.SiteVisitDoneCount = item.Value?.Select(j => j.SiteVisitDoneCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitDoneUniqueCount = item.Value?.Select(j => j.SiteVisitDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitDoneUniqueCountPercentage = GetCountPercentage(item.Value?.Select(j => j.SiteVisitDoneUniqueCount)?.Sum(i => i) ?? 0, totalCount);
                            value.SiteVisitNotDoneCount = item.Value?.Select(j => j.SiteVisitNotDoneCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitNotDoneUniqueCount = item.Value?.Select(j => j.SiteVisitNotDoneUniqueCount)?.Sum(i => i) ?? 0;
                            value.SiteVisitNotDoneUniqueCountPercentage = GetCountPercentage(item.Value?.Select(j => j.SiteVisitNotDoneUniqueCount)?.Sum(i => i) ?? 0, totalCount);
                            value.OverdueCount = item.Value?.Select(j => j.OverdueCount)?.Sum(i => i) ?? 0;
                            value.OverdueCountPercentage = GetCountPercentage(item.Value?.Select(j => j.OverdueCount)?.Sum(i => i) ?? 0, totalCount);
                            var allItems = item.Value?.Where(i => (i.StatusId != null))?.ToList();
                            if (allItems?.Any() ?? false)
                            {
                                value.Status = ReportHelper.MapCustomStatusAndChildStatus(allItems.Adapt<List<ViewStatusDto>>());
                                value.Status.ForEach(i =>
                                {
                                    i.Percentage = GetCountPercentage(i.Count, totalCount);
                                });
                            }
                            userDtos.Add(value);
                        }
                        var leadStatus = await _customMastereadStatus.ListAsync(cancellationToken);
                        var headers = ExportDataHelper.GetLeadHeaders<FormattedUserReportDto>(leadStatus, true);
                        int startIndex = 3;
                        if ((selectedColumns?.Any() ?? false) && selectedColumns != null && (request.ShouldShowPercentage == true))
                        {
                            List<string> newSelectedColumns = new List<string>(selectedColumns);
                            foreach (string status in selectedColumns)
                            {
                                newSelectedColumns.Add($"{status}count");
                                newSelectedColumns.Add($"{status}uniquecount");
                                newSelectedColumns.Add($"{status}uniquecountpercentage");
                            }
                            headers = headers.Where(i => selectedColumns.Contains(i.Header.Trim().Replace(" ", "")) || defaultColumns.Contains(i.Header.Trim().Replace(" ", "")) || newSelectedColumns.Contains(i.Header.Trim().Replace(" ", ""))).ToList();
                        }
                        else if (selectedColumns?.Any() ?? false && selectedColumns != null)
                        {
                            headers = headers.Where(i => selectedColumns.Contains(i.Header.Trim().Replace(" ", "")) || defaultColumns.Contains(i.Header.Trim().Replace(" ", ""))).ToList();
                        }
                        List<Lrb.Application.Reports.Web.Data.Dtos.Common.LeadHeadersDto> newHeaders = new();
                        if (request?.ShouldShowPercentage == true)
                        {
                            headers.ForEach(i =>
                            {
                                if (leadStatus.Any(j => j.DisplayName.Trim().ToLower().Replace(" ", "") == i.Header.Trim().ToLower().Replace(" ", "")))
                                {
                                    newHeaders.Add(new Lrb.Application.Reports.Web.Data.Dtos.Common.LeadHeadersDto() { Header = i.Header + " Percentage" });
                                }
                            });
                        }
                        if (newHeaders?.Any() ?? false)
                        {
                            headers.AddRange(newHeaders);
                        }
                        if ((selectedColumns?.Any() ?? false) && selectedColumns.Contains("GeneralManager"))
                        {
                            startIndex = 4;
                        }
                        if ((selectedColumns?.Any() ?? false) && selectedColumns.Contains("ReportingManager"))
                        {
                            startIndex = 5;
                        }
                        var initialPart = headers.Take(startIndex).ToList();
                        var partToSort = headers.Skip(startIndex).ToList();
                        partToSort = partToSort.OrderBy(i => i.Header.Trim()).ToList();
                        headers = initialPart.Concat(partToSort).ToList();
                        var dtos = userDtos.Adapt<List<FormattedUserReportDto>>();
                        dtos.ForEach(i =>
                        {
                            if (i.Status?.Any() ?? false)
                            {
                                List<ViewUserStatusDto> allStatus = new();
                                i.Status.ForEach(j =>
                                {
                                    var result = j.Adapt<ViewUserStatusDto>();
                                    result.StatusDisplayName += " Percentage";
                                    result.Count = result.Percentage;
                                    allStatus.Add(result);
                                });
                                i.Status.AddRange(allStatus);
                            }
                        });
                        var fileBytes = LeadExcelGeneration<FormattedUserReportDto>.GenerateExcel(dtos, "Users Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Reports/{tenantId ?? "Default"}", $"Export_Lead_User_Status_Report_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email);
                        isSent = true;
                        tracker.Count = dtos.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Lead_User_Status_Report_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadStatusReportByAgencyHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        private string GetCountPercentage(long count, float totalCount)
        {
            return (float)Math.Round((count / totalCount) * 100, 2) + " %";
        }
    }
}
