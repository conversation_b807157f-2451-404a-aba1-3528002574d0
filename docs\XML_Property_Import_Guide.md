# XML Property Import Guide

## Overview

This guide explains how to use the new XML property import functionality that allows you to import property data from external XML feeds into the LeadRat Black property module.

## Features

- **XML Feed Processing**: Fetches XML data from external URLs
- **Automatic Mapping**: Maps XML fields to CreatePropertyDto fields
- **Multi-language Support**: Handles multilingual content (English, Russian, Arabic)
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Error Handling**: Robust error handling with detailed error reporting
- **Batch Processing**: Processes multiple properties from a single XML feed

## API Endpoint

### Import Properties from XML Feed

**Endpoint**: `POST /api/v1/property/import/xml-feed`

**Headers**:
- `Authorization`: Bearer token
- `X-Tenant`: Tenant ID
- `Content-Type`: application/json

**Request Body**:
```json
{
  "url": "https://example.com/realty-feed.xml",
  "tenantId": "your-tenant-id",
  "currentUserId": "user-guid"
}
```

**Response**:
```json
{
  "data": {
    "totalOffers": 10,
    "processedOffers": 10,
    "successfulImports": 8,
    "failedImports": 2,
    "errors": [
      "Failed to create property from offer 1234: Invalid property type"
    ],
    "createdPropertyIds": [
      "guid1",
      "guid2",
      "guid3"
    ]
  },
  "succeeded": true,
  "message": "Import completed successfully"
}
```

## XML Feed Structure

The system expects XML feeds in the following format:

```xml
<realty-feed>
  <generation-date>2023-07-26T09:01:31+04:00</generation-date>
  <offers>
    <complex-id>1438</complex-id>
    <type>residential_complex</type>
    <title>
      <ru>Vita Grande</ru>
      <en>Vita Grande</en>
      <ar>فيتا غراندي</ar>
    </title>
    <description>
      <ru><![CDATA[Russian description]]></ru>
      <en><![CDATA[English description]]></en>
      <ar><![CDATA[Arabic description]]></ar>
    </description>
    <address>JVC, Dubai</address>
    <latitude>25.06009279</latitude>
    <longitude>55.21818638</longitude>
    <price>
      <min>816000</min>
      <max>1117000</max>
      <currency>AED</currency>
    </price>
    <!-- Additional fields... -->
  </offers>
</realty-feed>
```

## Field Mapping

The following XML fields are mapped to CreatePropertyDto properties:

| XML Field | CreatePropertyDto Field | Notes |
|-----------|------------------------|-------|
| `title.en` | `Title` | Prefers English, falls back to Russian, then Arabic |
| `description.en` | `AboutProperty` | Prefers English, falls back to Russian, then Arabic |
| `complex-id` | `SerialNo`, `RefrenceNo` | Used as reference identifier |
| `address` | `Address.FullAddress` | Full address string |
| `latitude` | `Address.Latitude` | Geographic coordinate |
| `longitude` | `Address.Longitude` | Geographic coordinate |
| `price.min` | `MonetaryInfo.Price` | Minimum price |
| `price.max` | `MonetaryInfo.MaxPrice` | Maximum price |
| `price.currency` | `MonetaryInfo.Currency` | Currency code |
| `br_prices.key` | `NoOfBHK`, `BHKType` | Bedroom count and type |
| `br_prices.min_area.m2` | `Dimension.Area` | Property area in square meters |
| `album.image` | `ImageUrls` | Property images |
| `amenities.amenity` | `AdditionalProperties["Amenities"]` | Amenities list |

## Additional Properties

The following XML fields are stored in the `AdditionalProperties` dictionary:

- `ComplexId`
- `Type`
- `Logo`
- `Photo`
- `ConstructionStartAt`
- `ConstructionProgress`
- `PlannedCompletionAt`
- `PredictedCompletionAt`
- `BuildingsCount`
- `ForSaleCount`
- `IsSoldOut`
- `UpdatedAt`
- `Developer`
- `SalesStatus`

## Usage Examples

### Using cURL

```bash
curl -X POST "https://your-api-domain/api/v1/property/import/xml-feed" \
  -H "Authorization: Bearer your-token" \
  -H "X-Tenant: your-tenant-id" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com/realty-feed.xml",
    "tenantId": "your-tenant-id",
    "currentUserId": "your-user-id"
  }'
```

### Using JavaScript/Fetch

```javascript
const response = await fetch('/api/v1/property/import/xml-feed', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'X-Tenant': tenantId,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    url: 'https://example.com/realty-feed.xml',
    tenantId: tenantId,
    currentUserId: userId
  })
});

const result = await response.json();
console.log('Import result:', result);
```

## Error Handling

The system provides comprehensive error handling:

- **Network Errors**: HTTP request failures when fetching XML
- **XML Parsing Errors**: Invalid XML format or structure
- **Mapping Errors**: Issues converting XML data to property objects
- **Validation Errors**: Property data that doesn't meet validation requirements

All errors are logged and returned in the response for debugging.

## Permissions

Users must have the `Create` permission for the `Properties` resource to use this endpoint.

## Logging

The system logs the following events:
- XML feed fetch attempts and results
- Property creation successes and failures
- Mapping errors and warnings
- Performance metrics

## Best Practices

1. **Test with Small Feeds**: Start with XML feeds containing a few properties to test the mapping
2. **Monitor Logs**: Check application logs for detailed error information
3. **Validate XML**: Ensure your XML feed follows the expected structure
4. **Handle Errors**: Implement proper error handling in your client applications
5. **Rate Limiting**: Be mindful of API rate limits when importing large feeds

## Troubleshooting

### Common Issues

1. **XML Parsing Errors**: Ensure your XML is well-formed and follows the expected structure
2. **Missing Required Fields**: Some CreatePropertyDto fields may be required - check validation errors
3. **Network Timeouts**: Large XML feeds may take time to process - consider breaking them into smaller chunks
4. **Permission Errors**: Ensure the user has proper permissions to create properties

### Support

For additional support or questions about the XML import functionality, please contact the development team or check the application logs for detailed error information.
