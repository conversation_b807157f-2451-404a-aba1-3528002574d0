﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="ISO3166" Version="1.0.4" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.15.1" />
		<!--<PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="6.0.0" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
	  <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
	  <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />-->
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
		<PackageReference Include="Nager.Country" Version="4.0.0" />
		<PackageReference Include="Npgsql" Version="6.0.7" />
		<PackageReference Include="RestSharp" Version="108.0.2" />

		<!-- Packages required for Configuration -->
		<PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="6.0.1" />
		<!---->

		<!-- Packages required for Logging -->
		<PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="6.0.0" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\Lrb.Infrastructure\Lrb.Infrastructure.csproj" />
	</ItemGroup>
</Project>

