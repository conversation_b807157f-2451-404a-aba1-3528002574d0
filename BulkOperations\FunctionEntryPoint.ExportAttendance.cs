﻿using Azure.Core;
using Lrb.Application.Attendance.Web.Dtos;
using Lrb.Application.Attendance.Web.Requests;
using Lrb.Application.Attendance.Web.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.UserDetails.Web.Request;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using System;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ExportAttendanceHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            ExportAttendanceTracker? exportTracker = await _exportAttendanceTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            try
            {
                exportTracker.Status = UploadStatus.InProgress;
                await _exportAttendanceTrackerRepo.UpdateAsync(exportTracker);


                ExportedDetailsDto? exportedDetails = exportTracker?.Adapt<ExportedDetailsDto>();
                //created by user details
                Guid creatredBy = exportTracker.CreatedBy;
                var creatredByuserDetails = await _userService.GetAsync(creatredBy.ToString(), cancellationToken);
                var fullUserDetails = await _userDetailsRepo.ListAsync(new UsersCountByFilterSpec(creatredBy), cancellationToken);
                if (fullUserDetails?.FirstOrDefault()?.ReportsTo != Guid.Empty)
                {
                    try
                    {
                        var reportsTo = await _userService.GetAsync(fullUserDetails?.FirstOrDefault()?.ReportsTo.ToString() ?? Guid.Empty.ToString(), cancellationToken);
                        exportedDetails.ReportTo = reportsTo?.FirstName + " " + reportsTo?.LastName;

                    }
                    catch (Exception ex) {
                    }
                }

                string firstName = creatredByuserDetails?.FirstName ?? string.Empty;
                string lastName = creatredByuserDetails?.LastName ?? string.Empty;
                exportedDetails.CreatedBy = firstName + " " + lastName;
                exportedDetails.Designation = fullUserDetails?.FirstOrDefault()?.Designation?.Name;
                exportedDetails.Department = fullUserDetails?.FirstOrDefault()?.Department?.Name;



                if (exportTracker.UserIds != null && exportTracker.UserIds.Count > 0)
                {
                    List<string> UsersIdString = exportTracker.UserIds.Select(guid => guid.ToString()).ToList();

                    var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                    string userstodetails = "";

                    if (usernameDetails != null && usernameDetails.Count > 0)
                    {
                        foreach (var userDetailsDto in usernameDetails)
                        {
                            string firstName1 = userDetailsDto.FirstName ?? string.Empty;
                            string lastName1 = userDetailsDto.LastName ?? string.Empty;

                            string userDetail = $"{firstName1} {lastName1}";

                            userstodetails += userDetail + ",";
                        }
                        userstodetails = userstodetails.TrimEnd(',');
                    }
                    exportedDetails.UsersNames = userstodetails;

                }

                //============================


                if (exportTracker != null)
                {
                    var request = JsonConvert.DeserializeObject<ExportAttendanceDto>(exportTracker?.Request ?? string.Empty);
                    ExportAttendanceRequest? requestforFileName = JsonConvert.DeserializeObject<ExportAttendanceRequest>(exportTracker?.Request ?? string.Empty);


                    exportedDetails.FromDate = request?.FromDate.Value.ToParticularTimeZone(requestforFileName?.TimeZoneId ?? string.Empty, requestforFileName.BaseUTcOffset).ToString("dd-MM-yyyy");
                    exportedDetails.ToDate = request?.ToDate.Value.ToParticularTimeZone(requestforFileName?.TimeZoneId ?? string.Empty, requestforFileName.BaseUTcOffset).ToString("dd-MM-yyyy");
                    exportedDetails.CreatedOn = exportTracker?.CreatedOn.ToParticularTimeZone(requestforFileName?.TimeZoneId, requestforFileName.BaseUTcOffset).ToString();


                    var tenantId = input.TenantId;
                    var userId = input.CurrentUserId;
                    List<Guid>? teamUserIds = new();
                    List<Guid>? filterIds = new();
                    var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                    if (isAdmin)
                    {
                        filterIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                    }
                    else if (request.ExportPermission != null)
                    {
                        switch (request.ExportPermission)
                        {
                            case ReportPermission.All:
                                filterIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                break;
                            case ReportPermission.Reportees:
                                filterIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                break;
                        }
                    }
                    if (request != null)
                    {
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            //var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                            if (!isAdmin)
                            {
                                teamUserIds = filterIds;
                                //request.UserIds = new List<Guid>() { userId };
                                // teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                        }

                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => filterIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = filterIds;
                        }
                        Console.WriteLine("User Ids Fetched With Team Without Team ");

                        if (teamUserIds != null)
                        {
                            if (request.UserIds == null)
                            {
                                request.UserIds = new List<Guid>();
                            }
                            request?.UserIds?.AddRange(teamUserIds);
                            request?.UserIds?.Distinct();
                        }
                        if ((request.FromDate == null || request.ToDate == null))
                        {
                            throw new InvalidDataException("Please provide either Month along with Year or FromDate along with ToDate!");
                        }
                        List<List<AttendanceReportDto>> attendanceReportByUser = new List<List<AttendanceReportDto>>();
                        var userDetails = await _userDetailsRepo.ListAsync(new UsersByFilterSpec(request), cancellationToken);
                        var totalCount = await _userDetailsRepo.CountAsync(new UsersCountByFilterSpec(request), cancellationToken);

                        DateTime fromDate = request.FromDate.Value.Date.ConvertFromDateToUtc();
                        DateTime toDate = request.ToDate.Value.Date.ConvertToDateToUtc();
                        var userIdsToTake = new List<string>();
                        userIdsToTake.AddRange(userDetails.Select(i => i.UserId.ToString()).ToList());
                        var users = await _userService.GetListOfUsersByIdsAsync(userIdsToTake, cancellationToken);

                        Console.WriteLine("Users Fetched ");

                        foreach (var user in userDetails)
                        {
                            if (user.Id == Guid.Empty || user.UserId == Guid.Empty)
                            {
                                continue;
                            }
                            request.UserIds = new() { user.UserId };
                            List<AttendanceLog> logs = await _attendanceRepository.ListAsync(new AttendanceLogSpec(request, fromDate, toDate), cancellationToken);
                            var logDtos = logs.Adapt<List<AttendanceLogReport>>();
                            var userDetailsDto = users.FirstOrDefault(i => i.Id == user.UserId);
                            if (userDetailsDto == null)
                            {
                                continue;

                            }
                            //var attendanceLogs = await GetLogsByDateAsync(logDtos, request.FromDate.Value, request.ToDate.Value, userDetailsDto);
                            var attendanceLogs = await GetLogsByDateAsync(logDtos, request.FromDate.Value, request.ToDate.Value, userDetailsDto,requestforFileName.TimeZoneId,requestforFileName.BaseUTcOffset);

                            if (attendanceLogs.Count == 0)
                            {
                                if (userDetailsDto.FirstName != null && userDetailsDto.FirstName != null && userDetailsDto.LastName != null)
                                {
                                    attendanceLogs.Add(new AttendanceReportDto { Name = userDetailsDto.FirstName + " " + userDetailsDto.LastName, UserName = userDetailsDto.UserName });
                                }
                                else
                                {
                                    continue;
                                }
                            }

                            attendanceReportByUser.Add(attendanceLogs);
                            Console.WriteLine($"Stored attendanceLogs For ->>{userDetailsDto.FirstName} ");
                        }
                        #region Excel Genaration
                        // var stream = EPPlusExcelHelper.GenerateExcelByObjectList(attendanceReportByUser);                       
                        var result = EPPlusExcelHelper.GenerateExcel(attendanceReportByUser, exportedDetails,requestforFileName.TimeZoneId, requestforFileName.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(requestforFileName.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"ValidatedExcel/{input.TenantId ?? "Default"}", $"Export_Attendence_" + input.TenantId + requestforFileName.FileName + "(" + timeZoneName + ")" + ".xlsx", result);
                        Console.WriteLine("Key came");
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        Console.WriteLine("Url genereted");
                        exportTracker.TotalUserCount = users.Count;
                        exportTracker.S3BucketKey = presignedUrl;
                        exportTracker.FileName = $"Export_Attendence_" + requestforFileName.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        exportTracker.Status = UploadStatus.Completed;
                        Console.WriteLine(presignedUrl);
                        await _exportAttendanceTrackerRepo.UpdateAsync(exportTracker);
                        #endregion
                    }
                }
            }
            catch (Exception e)
            {
                exportTracker.Message = e.Message;
                exportTracker.Status = UploadStatus.Failed;
                exportTracker.LastModifiedBy = input.CurrentUserId;
                exportTracker.CreatedBy = input.CurrentUserId;
                await _exportAttendanceTrackerRepo.UpdateAsync(exportTracker);
                Console.WriteLine($"Attendance Export  -> Exception {JsonConvert.SerializeObject(e, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;

            }
        }


        private async static Task<List<AttendanceReportDto>> GetLogsByDateAsync(List<AttendanceLogReport> userEntries, DateTime fromDate, DateTime toDate, UserDetailsDto userDetailsDto, string timeZoneId, TimeSpan baseUtcOffset)
        {
            List<AttendanceReportDto> attendanceReportDtos = new List<AttendanceReportDto>();
            Dictionary<DateTime, LogsByDayDto> entriesByDate = new();

            while (fromDate <= toDate)
            {
                var startDate = fromDate.Date.ConvertFromDateToUtc();
                var endDate = fromDate.Date.ConvertToDateToUtc();
                var res = userEntries.Where(i => (i.ClockInTime >= startDate && i.ClockInTime <= endDate)
                                                              || (i.ClockOutTime >= startDate && i.ClockOutTime <= endDate));
                // var entriesPerDay = res.Adapt<List<NewAttendanceLogReport>>();          
                TimeSpan? duration = TimeSpan.Zero;
                string formattedDuration = null;
                //  string dateOnly = startDate.Date.ToString("dd-MM-yyyy");
                foreach (var entry in res)
                {
                    DateTime? clockInTime = entry?.ClockInTime?.ToParticularTimeZone(timeZoneId, baseUtcOffset);
                    DateTime? clockOutTime = entry?.ClockOutTime?.ToParticularTimeZone(timeZoneId, baseUtcOffset);
                    string clockInTimeFormatted = clockInTime?.ToString("hh:mm:ss tt"); // AM/PM format
                    string clockOutTimeFormatted = clockOutTime?.ToString("hh:mm:ss tt"); // AM/PM format

                    duration = (clockOutTime?.TimeOfDay - clockInTime?.TimeOfDay);
                    if (duration < TimeSpan.Zero)
                    {
                        duration = TimeSpan.FromHours(12) - (clockOutTime?.TimeOfDay - clockInTime?.TimeOfDay);
                    }
                    try
                    {
                        attendanceReportDtos.Add(new AttendanceReportDto
                        {
                            Date = clockInTime?.Date.ToString("dd-MM-yyyy"),
                            Name = userDetailsDto.FirstName + " " + userDetailsDto.LastName,
                            UserName = userDetailsDto.UserName,
                            ClockInTime = clockInTimeFormatted,
                            ClockOutTime = clockOutTimeFormatted,
                            ClockInLocation = entry.ClockInLocation,
                            ClockOutLocation = entry.ClockOutLocation,
                            TotalWorkHours = duration?.ToString(@"hh\:mm\:ss") ?? "00:00:00",
                        });
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine($"Attendance Export  -> Exception {JsonConvert.SerializeObject(e, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                        throw;
                    }
                }
                fromDate = fromDate.AddDays(1);
            }
            //var result = attendanceReportDtos.Any(i => i.AttendanceLogReports.Any());
            // var result = attendanceReportDtos.Any(i => i.UserName.Any());
            return attendanceReportDtos;
        }

        private async static Task<TimeSpan> GetWorkingHours(AttendanceLogReport log)
        {
            TimeSpan hours = new();
            if (log.ClockInTime != null && log.ClockOutTime != null)
            {
                var time = (log.ClockOutTime.Value) - (log.ClockInTime.Value);
                hours = hours + time;
            }

            return hours;
        }
    }
}
