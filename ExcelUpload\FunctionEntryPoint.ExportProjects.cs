﻿using Lrb.Application.Email.Web;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Project.Web;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Project.Web.Requests;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ExportProjectsHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var globalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            if (globalSettings != null && globalSettings.IsProjectsExportEnabled)
            {
                ExportProjectTracker? exportTracker = await _exportProjectRepo.GetByIdAsync(input.TrackerId, cancellationToken);
                RunAWSBatchForExportProjectsRequest? requestforFileName = JsonConvert.DeserializeObject<RunAWSBatchForExportProjectsRequest>(exportTracker?.Request ?? string.Empty);
                var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
                var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
                var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ExportLead), CancellationToken.None)).FirstOrDefault();
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                bool isSent = false;
                Guid CreatredById = exportTracker.CreatedBy;
                string trackerIdString = CreatredById.ToString();
                ExportTrackerDto? tracker = exportTracker?.Adapt<ExportTrackerDto>();
                List<Lrb.Application.Identity.Users.UserDetailsDto> users = new(await _userService.GetListAsync(cancellationToken));
                var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
                string firstName = userDetails.Result.FirstName;
                string lastName = userDetails.Result.LastName;
                string createdBy = $"{firstName} {lastName}";
                tracker.CreatedBy = createdBy;
                try
                {
                    if (tracker != null && serviceProvider != null && exportEmailTemplate != null)
                    {
                        RunAWSBatchForExportProjectsRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForExportProjectsRequest>(exportTracker?.Request ?? string.Empty);
                        if (request != null)
                        {
                            ProjectExportFilterDto? filtersDto = request.Adapt<ProjectExportFilterDto>();
                            ProjectFormettedExportFilter formattedFiltersDto = filtersDto.Adapt<ProjectFormettedExportFilter>();
                            var projects = await _projectRepo.ListAsync(new ProjectByCustomFilterSpec(request), cancellationToken);
                            var count = await _projectRepo.CountAsync(new ProjectByCustomFilterSpec(request), cancellationToken);
                            var viewProjects = projects.Adapt<List<ExportPropertDto>>();
                            List<ProjectExportFormattedV2Dto> resultProjectDtos = new List<ProjectExportFormattedV2Dto>();
                            foreach (var projectDto in viewProjects)
                            {
                                if (projectDto == null) continue;

                                IEnumerable<string>? allAmenities = null;
                                List<string>? bankNames = null;

                                if (projectDto.Amenities?.Any() ?? false)
                                {
                                    var amenityIds = projectDto.Amenities.Select(a => a.MasterProjectAmenityId).ToList();
                                    allAmenities = await _dapperRepository.GetAllAmenitiesForProjetAsync(input.TenantId ?? string.Empty, amenityIds);
                                }

                                if (projectDto.AssociatedBanks?.Any() ?? false)
                                {
                                    var masterAssociatesBankIds = projectDto.AssociatedBanks.Select(i => i.MasterAssociatedBankId).ToList();
                                    if (masterAssociatesBankIds.Any())
                                    {
                                        var associatesBanks = await _associatesBankRepo.ListAsync(new GetProjectAssociatesBankByIdsSpec(masterAssociatesBankIds), cancellationToken);
                                        bankNames = associatesBanks.Adapt<List<AssociatedBankDto>>().Select(b => b.Name).ToList();
                                    }
                                }
                                ProjectExportFormattedV2Dto formattedProject = MappingProjectDto(projectDto, allAmenities, bankNames, users);
                                resultProjectDtos.Add(formattedProject);
                            }
                            var exportTemplate = await _exportTemplateRepo.GetByIdAsync(exportTracker?.TemplateId ?? Guid.Empty);
                            var fileBytes = ExcelGeneration<ProjectExportFormattedV2Dto>.GenerateExcel(resultProjectDtos, "Export Project", formattedFiltersDto, tracker, request.TimeZoneId, request.BaseUTcOffset);
                            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Projects/{input.TenantId ?? "Default"}", $"Export_Projects_" + input.TenantId + requestforFileName.FileName + ".xlsx", fileBytes, 0);
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            List<string> toEmails = new();
                            List<string> ccEamils = new();
                            List<string> bccEamils = new();
                            if (exportTracker?.ToRecipients?.Any() ?? false)
                            {
                                toEmails.AddRange(exportTracker.ToRecipients);
                            }
                            if (exportTracker?.CcRecipients?.Any() ?? false)
                            {
                                ccEamils.AddRange(exportTracker.CcRecipients);
                            }
                            if (exportTracker?.BccRecipients?.Any() ?? false)
                            {
                                bccEamils.AddRange(exportTracker.BccRecipients);
                            }
                            var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
                            emailSenderDto.To = toEmails;
                            emailSenderDto.Cc = ccEamils;
                            emailSenderDto.Bcc = bccEamils;
                            emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                            emailSenderDto.EmailBody = template;
                            emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                            emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
                            await _graphEmailService.SendEmail(emailSenderDto);
                            isSent = true;
                            exportTracker.Count = projects.Count();
                            exportTracker.S3BucketKey = presignedUrl;
                            exportTracker.Template = JsonConvert.SerializeObject(exportTemplate);
                            exportTracker.LastModifiedBy = input.CurrentUserId;
                            exportTracker.FileName = $"Export_Properties_" + requestforFileName.FileName + ".xlsx";
                            exportTracker.CreatedBy = input.CurrentUserId;
                            await _exportProjectRepo.UpdateAsync(exportTracker, cancellationToken);
                        }
                    }
                }
                catch (Exception e)
                {
                }
            }
        }
        public static ProjectExportFormattedV2Dto MappingProjectDto(ExportPropertDto dto, IEnumerable<string>? allAmenities = null, List<string>? banks = null, List<Lrb.Application.Identity.Users.UserDetailsDto>? users = null)
        {
            if (dto == null)
            {
                return null;
            }
            var formattedProject = new ProjectExportFormattedV2Dto
            {
                Name = dto.Name,
                ProjectStatus = dto.Status.ToString(),
                ProjectMaxPrice = dto.MaximumPrice,
                ProjectMinPrice = dto.MinimumPrice,
                LandArea = dto.Area,
                Certificates = dto.Certificates,
                Facings = ConvertToString(dto.Facings),
                TotalBlocks = dto.TotalBlocks,
                TotalUnits = dto?.TotalFlats,
                TotalFloor = dto.TotalFloor,
                Description = dto.Description,
                PossessionDate = dto?.PossessionDate,
                ReraNumbers = ConvertToString(dto.ReraNumbers),
                Location = dto.Address?.Locality + " " + dto.Address?.SubLocality,
                AllAmenities = ConvertToString(allAmenities),
                AssociatedBanks = ConvertToString(banks),

            };
            formattedProject.ProjectType = dto.ProjectType?.DisplayName;
            formattedProject.ProjectSubType = dto?.ProjectType?.ChildType?.DisplayName;
            formattedProject.BuilderName = dto?.BuilderDetail?.Name;
            formattedProject.BuilderContactNumber = dto?.BuilderDetail?.ContactNo;
            formattedProject.BrokerageAmount = dto?.MonetaryInfo?.Brokerage;
            formattedProject.AssinedTo = users != null && dto?.AssignedUserIds != null
            ? string.Join(", ", users
                .Where(i => dto.AssignedUserIds.Contains(i.Id))
                .Select(u => $"{u.FirstName} {u.LastName}"))
            : string.Empty;
            return formattedProject;
        }
        private static string ConvertToString(List<string>? numbers)
        {
            numbers = numbers?.Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            if (numbers == null || numbers.Count == 0)
                return null;
            List<string> forstringsList = numbers.Select(e => e.ToString()).ToList();
            return string.Join(", ", forstringsList);
        }
        private static string? ConvertToString(IEnumerable<string>? allAmenities)
        {
            var filteredallAmenities = allAmenities?.Where(i => !string.IsNullOrWhiteSpace(i));
            return filteredallAmenities == null || !filteredallAmenities.Any() ? null : string.Join(", ", filteredallAmenities);
        }
    }
}
