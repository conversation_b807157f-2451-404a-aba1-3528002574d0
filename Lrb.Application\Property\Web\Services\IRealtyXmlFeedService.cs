using Lrb.Application.Property.Web.Dtos.XMl_Feed;

namespace Lrb.Application.Property.Web.Services
{
    public interface IRealtyXmlFeedService : ITransientService
    {
        /// <summary>
        /// Fetches XML data from the specified URL and deserializes it to RealtyFeedDto
        /// </summary>
        /// <param name="url">The URL to fetch XML data from</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Parsed RealtyFeedDto object</returns>
        Task<RealtyFeedDto> FetchRealtyFeedAsync(string url, CancellationToken cancellationToken = default);
    }
}
