﻿using Lrb.Application.Common.Interfaces;
using Lrb.Domain.Enums;
using System;
using System.Collections.Generic;

namespace ExcelUploadExecutors.Dtos
{
    public class AutomationReportDto : IDto
    {
        public Guid Id { get; set; }
        public string TenantId { get; set; }
        public Guid CurrentUserId { get; set; }
        public string Type { get; set; }
        public List<string>? ToRecipients { get; set; }
        public string? Request { get; set; }
    }
    public class ReportsConfigurationDto : IDto
    {
        public Guid Id { get; set; }
        public string? ReportName { get; set; }
        public int? Frequency { get; set; }
        public ScheduleType? ScheduleType { get; set; }
        public TimeOnly? ScheduleTime { get; set; }
        public int? Duration { get; set; }
        public List<int>? SelectedDays { get; set; }
        public List<ServiceType>? ServiceType { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? Filter { get; set; }
        public string? JobId { get; set; }
        public bool IsEnabled { get; set; }
        public string? TenantId { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? Env { get; set; }
        public Guid? WATemplateId { get; set; }
        public List<string>? PhoneNumbers { get; set; }
    }
}
