using Lrb.Application.Property.Web.Dtos.XMl_Feed;
using Lrb.Application.Property.Web.Services;
using System.Xml.Serialization;

namespace Lrb.Infrastructure.Property.Services
{
    public class RealtyXmlFeedService : IRealtyXmlFeedService
    {
        private readonly HttpClient _httpClient;
        private readonly Serilog.ILogger _logger;

        public RealtyXmlFeedService(HttpClient httpClient, Serilog.ILogger logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        public async Task<RealtyFeedDto> FetchRealtyFeedAsync(string url, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.Information("Fetching realty XML feed from URL: {Url}", url);

                // Fetch XML content from URL
                var response = await _httpClient.GetAsync(url, cancellationToken);
                response.EnsureSuccessStatusCode();

                var xmlContent = await response.Content.ReadAsStringAsync(cancellationToken);

                _logger.Information("Successfully fetched XML content. Length: {Length} characters", xmlContent.Length);
                _logger.Debug("XML Content Preview: {Preview}", xmlContent.Length > 500 ? xmlContent.Substring(0, 500) + "..." : xmlContent);

                // Check if content is HTML instead of XML
                if (xmlContent.TrimStart().StartsWith("<html", StringComparison.OrdinalIgnoreCase) ||
                    xmlContent.TrimStart().StartsWith("<!DOCTYPE html", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.Error("Received HTML content instead of XML from URL: {Url}", url);
                    throw new InvalidOperationException($"The URL {url} returned HTML content instead of XML. Please check the URL and ensure it serves raw XML content.");
                }

                // Check if content looks like XML
                if (!xmlContent.TrimStart().StartsWith("<?xml") && !xmlContent.TrimStart().StartsWith("<realty-feed"))
                {
                    _logger.Error("Content does not appear to be valid XML. Content starts with: {ContentStart}",
                        xmlContent.Length > 100 ? xmlContent.Substring(0, 100) : xmlContent);
                    throw new InvalidOperationException($"The content from URL {url} does not appear to be valid XML format.");
                }

                // Deserialize XML to RealtyFeedDto
                var serializer = new XmlSerializer(typeof(RealtyFeedDto));
                using var stringReader = new StringReader(xmlContent);
                
                var realtyFeed = (RealtyFeedDto?)serializer.Deserialize(stringReader);
                
                if (realtyFeed == null)
                {
                    throw new InvalidOperationException("Failed to deserialize XML content to RealtyFeedDto");
                }

                _logger.Information("Successfully parsed XML feed. Found {OffersCount} offers", realtyFeed.Offers?.Count ?? 0);

                return realtyFeed;
            }
            catch (HttpRequestException ex)
            {
                _logger.Error(ex, "HTTP error occurred while fetching XML feed from {Url}", url);
                throw new InvalidOperationException($"Failed to fetch XML feed from URL: {url}", ex);
            }
            catch (System.Xml.XmlException ex)
            {
                _logger.Error(ex, "XML parsing error occurred while processing feed from {Url}. Line: {LineNumber}, Position: {LinePosition}",
                    url, ex.LineNumber, ex.LinePosition);
                throw new InvalidOperationException($"Failed to parse XML content from URL: {url}. XML Error at line {ex.LineNumber}, position {ex.LinePosition}: {ex.Message}", ex);
            }
            catch (InvalidOperationException ex) when (ex.InnerException is System.Xml.XmlException xmlEx)
            {
                _logger.Error(ex, "XML serialization error occurred while processing feed from {Url}. Line: {LineNumber}, Position: {LinePosition}",
                    url, xmlEx.LineNumber, xmlEx.LinePosition);
                throw new InvalidOperationException($"Failed to deserialize XML content from URL: {url}. XML Error at line {xmlEx.LineNumber}, position {xmlEx.LinePosition}: {xmlEx.Message}", ex);
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex, "Serialization error occurred while processing feed from {Url}", url);
                throw;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Unexpected error occurred while fetching XML feed from {Url}", url);
                throw new InvalidOperationException($"Unexpected error occurred while processing XML feed from URL: {url}. Error: {ex.Message}", ex);
            }
        }
    }
}
