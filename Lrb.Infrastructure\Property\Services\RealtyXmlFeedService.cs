using Lrb.Application.Property.Web.Dtos.XMl_Feed;
using Lrb.Application.Property.Web.Services;
using System.Xml.Serialization;

namespace Lrb.Infrastructure.Property.Services
{
    public class RealtyXmlFeedService : IRealtyXmlFeedService
    {
        private readonly HttpClient _httpClient;
        private readonly Serilog.ILogger _logger;

        public RealtyXmlFeedService(HttpClient httpClient, Serilog.ILogger logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        public async Task<RealtyFeedDto> FetchRealtyFeedAsync(string url, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.Information("Fetching realty XML feed from URL: {Url}", url);

                // Fetch XML content from URL
                var response = await _httpClient.GetAsync(url, cancellationToken);
                response.EnsureSuccessStatusCode();

                var xmlContent = await response.Content.ReadAsStringAsync(cancellationToken);
                
                _logger.Information("Successfully fetched XML content. Length: {Length} characters", xmlContent.Length);

                // Deserialize XML to RealtyFeedDto
                var serializer = new XmlSerializer(typeof(RealtyFeedDto));
                using var stringReader = new StringReader(xmlContent);
                
                var realtyFeed = (RealtyFeedDto?)serializer.Deserialize(stringReader);
                
                if (realtyFeed == null)
                {
                    throw new InvalidOperationException("Failed to deserialize XML content to RealtyFeedDto");
                }

                _logger.Information("Successfully parsed XML feed. Found {OffersCount} offers", realtyFeed.Offers?.Count ?? 0);

                return realtyFeed;
            }
            catch (HttpRequestException ex)
            {
                _logger.Error(ex, "HTTP error occurred while fetching XML feed from {Url}", url);
                throw new InvalidOperationException($"Failed to fetch XML feed from URL: {url}", ex);
            }
            //catch (XmlException ex)
            //{
            //    _logger.Error(ex, "XML parsing error occurred while processing feed from {Url}", url);
            //    throw new InvalidOperationException($"Failed to parse XML content from URL: {url}", ex);
            //}
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex, "Serialization error occurred while processing feed from {Url}", url);
                throw;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Unexpected error occurred while fetching XML feed from {Url}", url);
                throw new InvalidOperationException($"Unexpected error occurred while processing XML feed from URL: {url}", ex);
            }
        }
    }
}
