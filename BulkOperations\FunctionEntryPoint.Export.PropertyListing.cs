﻿using Lrb.Application.Email.Web;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.BlobStorage;
using Mapster;
using Newtonsoft.Json;
using System.Globalization;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ExportPropertiesListingHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            List<Lrb.Domain.Entities.GlobalSettings> globalSettings1 = await _globalSettingsRepository.ListAsync(cancellationToken);
            var globalSettings = globalSettings1.FirstOrDefault();
            var awsBaseUrl = _awsSettings.AWSS3BucketUrl;
            if (globalSettings != null && globalSettings.IsPropertiesExportEnabled)
            {
                ExportPropertyTracker? exportTracker = await _exportPropertyRepo.GetByIdAsync(input.TrackerId, cancellationToken);
                RunAWSBatchForExportPropertiesForListingManagementRequest? requestforFileName = JsonConvert.DeserializeObject<RunAWSBatchForExportPropertiesForListingManagementRequest>(exportTracker?.Request ?? string.Empty);
                var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
                var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
                var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
                var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ExportLead), CancellationToken.None)).FirstOrDefault();
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                bool isSent = false;

                Guid CreatredById = exportTracker.CreatedBy;
                string trackerIdString = CreatredById.ToString();
                ExportTrackerDto? tracker = exportTracker?.Adapt<ExportTrackerDto>();
                var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
                string firstName = userDetails.Result.FirstName;
                string lastName = userDetails.Result.LastName;
                string createdBy = $"{firstName} {lastName}";
                tracker.CreatedBy = createdBy;
                var exportTracker1 = new ExportTrackerDto
                {
                    CreatedBy = createdBy,
                };
                try
                {
                    if (exportTracker != null && serviceProvider != null && exportEmailTemplate != null)
                    {
                        #region Fetch all required MasterData and Other data
                        GetAllPropertyForListingManagementRequest? request = JsonConvert.DeserializeObject<GetAllPropertyForListingManagementRequest>(exportTracker?.Request ?? string.Empty);
                        if (request != null)
                        {
                            PropertyExportFilterForListingManagement? filtersDto = request.Adapt<PropertyExportFilterForListingManagement>();
                            PropertyFormettedExportFilterForListingManagement formattedFiltersDto = filtersDto.Adapt<PropertyFormettedExportFilterForListingManagement>();
                            var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                            CustomMasterAttribute? masterPropertyAttribute = null;
                            CustomMasterAmenity? masterPropertyAmenity = null;
                            MasterPropertyType? masterPropertyType = null;
                            var masterAreaunites = await _masterAreaUnitRepo.ListAsync();
                            List<Guid>? allUserIds = request?.UserIds;
                            List<Guid> propertySubTypeId = request?.PropertySubTypes;
                            if (allUserIds?.Any() ?? false)
                            {
                                List<string> userIdsInString = allUserIds.Select(guid => guid.ToString()).ToList();
                                var usersDetails = await _userService.GetListOfUsersByIdsAsync(userIdsInString, cancellationToken);
                                string userNames = "";
                                if (userDetails != null && usersDetails.Count > 0)
                                {
                                    foreach (var userDetail in usersDetails)
                                    {
                                        string userName = $"{userDetail.FirstName} {userDetail.LastName}";
                                        userNames += userName + ",";
                                    }
                                    userNames = userNames.TrimEnd(',');
                                }
                                formattedFiltersDto.UserNames = userNames;
                            }
                            if (!string.IsNullOrWhiteSpace(request.PropertySearch))
                            {
                                masterPropertyAttribute = (await _masterPropertyAttributeRepo.FirstOrDefaultAsync(new GetMasterPropertyAttributeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken));
                                masterPropertyAmenity = (await _masterPropertyAmenityRepo.FirstOrDefaultAsync(new GetMasterPropertyAmenitySpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken));
                                masterPropertyType = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken));
                            }
                            var masterPropertyAttributes = (await _masterPropertyAttributeRepo.ListAsync(new GetAllCustomMasterAttributeSpec(), cancellationToken));
                            List<Guid>? propertyDimensionIds = new();
                            //if (request != null && request.PropertySize != null && request.PropertySize.AreaUnitId != default)
                            //{
                            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.AreaUnitId);
                            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
                            //    var area = request.PropertySize.Area * request.PropertySize.ConversionFactor;
                            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.Area * Lrb.Application.Property.Web.PropertySearchHelper.GetConversionFactor(i.AreaUnitId, _masterAreaUnitRepo).Result) == area).Select(i => i.Id).ToList();
                            //}
                            if (request != null && (request?.BHKs?.Any() ?? false))
                            {
                                formattedFiltersDto.NoOfBHKs = string.Join(", ", request.BHKs.Select(i => i.ToString()).ToList()) ?? string.Empty;
                            }
                            if (propertySubTypeId != null && propertySubTypeId.Count > 0)
                            {
                                var displayName = propetyTypes
                                    .Where(i => propertySubTypeId.Contains(i?.Id ?? Guid.Empty) && i?.Level == 1)
                                    .Select(status => status?.DisplayName)
                                    .ToList();

                                formattedFiltersDto.PropertySubTypes = string.Join(", ", displayName);
                            }

                            PropertyTypeBaseId propertyTypeIds = new();
                            propertyTypeIds.ResidentialBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("residential")))?.Id;
                            propertyTypeIds.AgricultureBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("agricultural")))?.Id;
                            propertyTypeIds.CommercialBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("commercial")))?.Id;

                            try
                            {

                                NumericAttributesDto numericAttributeDto = new NumericAttributesDto();
                                var tenantId = input?.TenantId;
                                var currentUserId = input?.CurrentUserId ?? Guid.Empty;
                                List<Guid>? userIds = new();
                                List<Guid>? filterIds = new();
                                List<Guid>? teamUserIds = new();
                                bool showAllProperties = false;

                                try
                                {
                                    switch (request.Permission)
                                    {
                                        case ViewAssignmentsPermission.View:
                                            if (request.UserIds?.Any() ?? false)
                                            {
                                                filterIds.AddRange(request.UserIds);
                                                if (request.IsWithTeam ?? false)
                                                {
                                                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                                    filterIds.AddRange(teamUserIds);
                                                }
                                                userIds.AddRange(filterIds);
                                            }
                                            else
                                            {
                                                userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                                showAllProperties = true;
                                            }
                                            break;
                                        case ViewAssignmentsPermission.ViewAssigned:
                                            userIds.Add(currentUserId);
                                            break;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    var error = new LrbError()
                                    {
                                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                        ErrorSource = ex?.Source,
                                        StackTrace = ex?.StackTrace,
                                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                        ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                                    };
                                    await _leadRepositoryAsync.AddErrorAsync(error);
                                }
                                var properties = await _propertyRepo.ListAsync(new PropertyListingByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, propertyTypeIds, numericAttributeDto, userIds, showAllProperties), cancellationToken);
                                List<ViewPropertyDto> propertyDtos = new List<ViewPropertyDto>();
                                try
                                {
                                    propertyDtos = properties.Adapt<List<ViewPropertyDto>>();
                                }
                                catch (Exception e)
                                {

                                }

                                List<ViewFormattedPropertyListingDto> resultPropertyDtos = new List<ViewFormattedPropertyListingDto>();
                                foreach (var propertyDto in propertyDtos)
                                {
                                    if (propertyDto != null && (propertyDto.Attributes?.Any() ?? false))
                                    {
                                        List<PropertyAttributeDto> attributes = new();
                                        foreach (var attribute in propertyDto.Attributes)
                                        {
                                            var masterAttribute = masterPropertyAttributes.Where(i => i.Id == attribute.MasterPropertyAttributeId).FirstOrDefault();
                                            if (masterAttribute != null)
                                            {
                                                attribute.AttributeName = masterAttribute.AttributeDisplayName;
                                                attributes.Add(attribute);
                                            }
                                            else
                                            {
                                                attributes.Add(attribute);
                                            }
                                        }
                                        propertyDto.Attributes = attributes;
                                        if (propertyDto != null && propertyDto.Dimension != null && propertyDto.Dimension.AreaUnitId != Guid.Empty)
                                        {
                                            //var dimension = (await _masterAreaUnitRepo.ListAsync(new GetMasterAreaUnitByAreaIdSpec(propertyDto.Dimension.AreaUnitId), cancellationToken)).FirstOrDefault();
                                            //propertyDto.Dimension.Unit = dimension?.Unit ?? null;
                                        }
                                        ViewFormattedPropertyListingDto formattedProperty = ListingMapping(propertyDto, awsBaseUrl);
                                        resultPropertyDtos.Add(formattedProperty);
                                    }
                                    else if (propertyDto != null)
                                    {
                                        if (propertyDto != null && propertyDto.Dimension != null && propertyDto.Dimension.AreaUnitId != Guid.Empty)
                                        {
                                            //var dimension = (await _masterAreaUnitRepo.ListAsync(new GetMasterAreaUnitByAreaIdSpec(propertyDto.Dimension.AreaUnitId), cancellationToken)).FirstOrDefault();
                                            //propertyDto.Dimension.Unit = dimension?.Unit ?? null;
                                        }
                                        ViewFormattedPropertyListingDto formattedProperty = ListingMapping(propertyDto, awsBaseUrl);
                                        resultPropertyDtos.Add(formattedProperty);
                                    }
                                }
                                if (request != null && request.Permission == ViewAssignmentsPermission.None)
                                {
                                    resultPropertyDtos = null;
                                }

                                var exportTemplate = await _exportTemplateRepo.GetByIdAsync(exportTracker?.TemplateId ?? Guid.Empty);
                                var fileBytes = Lrb.Application.Utils.ExcelGeneration<ViewFormattedPropertyListingDto>.GenerateExcel<ViewFormattedPropertyListingDto, PropertyFormettedExportFilterForListingManagement, ExportTrackerDto>(resultPropertyDtos, "Export Properties", formattedFiltersDto, tracker, requestforFileName.TimeZoneId, requestforFileName.BaseUTcOffset);
                                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Properties/{input.TenantId ?? "Default"}", $"Export_Properties_" + input.TenantId + requestforFileName.FileName + ".xlsx", fileBytes, 0);
                                var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                                List<string> toEmails = new();
                                List<string> ccEamils = new();
                                List<string> bccEamils = new();
                                if (exportTracker?.ToRecipients?.Any() ?? false)
                                {
                                    toEmails.AddRange(exportTracker.ToRecipients);
                                }
                                if (exportTracker?.CcRecipients?.Any() ?? false)
                                {
                                    ccEamils.AddRange(exportTracker.CcRecipients);
                                }
                                if (exportTracker?.BccRecipients?.Any() ?? false)
                                {
                                    bccEamils.AddRange(exportTracker.BccRecipients);
                                }
                                var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
                                emailSenderDto.To = toEmails;
                                emailSenderDto.Cc = ccEamils;
                                emailSenderDto.Bcc = bccEamils;
                                emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                                emailSenderDto.EmailBody = template;
                                emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                                emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
                                await _graphEmailService.SendEmail(emailSenderDto);
                                isSent = true;
                                exportTracker.Count = properties.Count();
                                exportTracker.S3BucketKey = presignedUrl;
                                exportTracker.Template = JsonConvert.SerializeObject(exportTemplate);
                                exportTracker.LastModifiedBy = input.CurrentUserId;
                                exportTracker.FileName = $"Export_Properties_" + requestforFileName.FileName + ".xlsx";
                                exportTracker.CreatedBy = input.CurrentUserId;
                                await _exportPropertyRepo.UpdateAsync(exportTracker, cancellationToken);
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                        #endregion
                    }
                }
                catch (Exception ex)
                {
                }
            }
        }

        #region Listing Management
        public static ViewFormattedPropertyListingDto ListingMapping(ViewPropertyDto viewPropertyDto, string? awsBaseUrl)
        {
            if (viewPropertyDto == null)
            {
                return null;
            }
            var formattedProperty = new ViewFormattedPropertyListingDto
            {
                Title = viewPropertyDto.Title,
                SaleType = viewPropertyDto.SaleType.ToString(),
                EnquiredFor = viewPropertyDto.EnquiredFor.ToString(),
                Notes = viewPropertyDto.Notes,
                FurnishStatus = viewPropertyDto.FurnishStatus.ToString(),
                Status = viewPropertyDto.Status.ToString(),
                Rating = viewPropertyDto.Rating,
                ShareCount = viewPropertyDto.ShareCount,
                PossessionDate = viewPropertyDto?.PossessionDate,
                Facing = viewPropertyDto.Facing.ToString(),
                BHK = viewPropertyDto.NoOfBHK,
                AboutProperty = viewPropertyDto.AboutProperty,
                Budget = viewPropertyDto?.MonetaryInfo?.ExpectedPrice,
                Location = viewPropertyDto?.Address?.Locality + " " + viewPropertyDto?.Address?.SubLocality,
                Attributes = ConvertListToString(viewPropertyDto?.Attributes),
                AreaWithUnit = $"{viewPropertyDto?.Dimension?.Area} {viewPropertyDto?.Dimension?.Unit}" ?? string.Empty,
                ThirdPartyURL = ConvertListStringToString(viewPropertyDto?.Links),
                ListingLevel = viewPropertyDto?.ListingLevel.ToString() ?? string.Empty,
                OfferingType = viewPropertyDto?.OfferingType.ToString() ?? string.Empty,
                CompletionStatus = viewPropertyDto?.CompletionStatus.ToString(),
                SubCommunity = viewPropertyDto?.Address?.SubCommunity,
                Community = viewPropertyDto?.Address?.Community,
                View360Url = ConvertListStringToString(viewPropertyDto?.View360Url),
                ImageUrls = ConvertListStringToString(viewPropertyDto?.ImageUrls?.SelectMany(kvp => kvp.Value.Select(img => AddBaseUrl(awsBaseUrl,img.ImageFilePath))).Where(url => !string.IsNullOrWhiteSpace(url)).ToList()),
                Videos = ConvertListStringToString(viewPropertyDto?.Videos?.Select(v => AddBaseUrl(awsBaseUrl, v.ImageFilePath)).Where(url => !string.IsNullOrWhiteSpace(url)).ToList()),
                Brochures = ConvertListStringToString(viewPropertyDto?.Brochures?.Select(b => AddBaseUrl(awsBaseUrl, b.URL)).Where(url => !string.IsNullOrWhiteSpace(url)).ToList()),
                PossesionType = viewPropertyDto?.PossesionType,
                SecurityDepositAmount = viewPropertyDto?.SecurityDepositAmount,
                SecurityDepositUnit = viewPropertyDto?.SecurityDepositUnit,

            };
            formattedProperty.OwnerName = viewPropertyDto.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.Name)) : null;
            formattedProperty.OwnerPhoneNo = viewPropertyDto.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.Phone)) : null;
            formattedProperty.OwnerEmail = viewPropertyDto.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.Email)) : null;
            formattedProperty.OwnerAlternateNo = viewPropertyDto.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.AlternateContactNo)) : null;
            formattedProperty.PropertyType = viewPropertyDto?.PropertyType?.DisplayName?.ToString() + ", " + viewPropertyDto?.PropertyType?.ChildType?.DisplayName?.ToString();
            formattedProperty.Project = viewPropertyDto?.Project;
            return formattedProperty;
        }
        #endregion
    }
}
