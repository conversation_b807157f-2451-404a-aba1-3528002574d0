using Lrb.Application.Property.Web.Services;
using Lrb.Infrastructure.Property.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Infrastructure.Property
{
    public static class Startup
    {
        public static IServiceCollection AddPropertyServices(this IServiceCollection services, IConfiguration config)
        {
            // Register HttpClient for RealtyXmlFeedService
            services.AddHttpClient<IRealtyXmlFeedService, RealtyXmlFeedService>(client =>
            {
                client.Timeout = TimeSpan.FromMinutes(5); // Set timeout for XML feed requests
                client.DefaultRequestHeaders.Add("User-Agent", "LeadRat-PropertyImporter/1.0");
            });

            return services;
        }
    }
}
