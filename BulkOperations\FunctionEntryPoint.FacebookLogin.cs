﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Integration.Web.Requests;
using Lrb.Application.Integration.Web.Requests.Facebook;
using Newtonsoft.Json;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint : FBCommonHandler, IFunctionEntryPoint
    {
        public async Task FacebookLoginHandler(InputPayload input)
        {
            Console.WriteLine("FunctionEntryPoint -> FacebookLoginHandler() started");
            var tenantId = input.TenantId;
            var request = JsonConvert.DeserializeObject<CreateFacebookIntegrationUsingconsoleRequest>(input.JsonData ?? "null");
            if (!string.IsNullOrEmpty(tenantId) && request != null)
            {
                var tInfo = await _repository.GetTenantInfoAsync(tenantId);
                var dto = new FacebookIntegrationDto()
                {
                    AccessToken = request.AccessToken,
                    FacebookUserId = request.FacebookUserId,
                    TenantInfoDto = tInfo,
                    CurrentUserId = _currentUser.GetUserId()
                };
                if (IsValidRequest(dto))
                {
                    if (!await IsAccountExistsInDifferentTenant(dto))
                    {
                        if (await IsAccountExistsInSameTenant(dto))
                        {
                            await UpdateFacebookAuthResponse(dto);
                        }
                        else
                        {
                            await CreateFacebookAuthResponse(dto);
                        }
                    }
                }
            }
            Console.WriteLine("FunctionEntryPoint -> FacebookLoginHandler() finished");
        }
    }
}
