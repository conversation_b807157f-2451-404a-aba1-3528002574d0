﻿using Lrb.Application.Common.PushNotification;
using Lrb.Application.DataManagement.Mobile.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.LeadCallLog.Mobile;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class BulkConvertToLeadRequest : IRequest<Response<bool>>
    {
        public List<Guid> Ids { get; set; }
        public List<Guid> AssignTo { get; set; }
        public List<string>? Projects { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public Guid? LeadStatusId { get; set; }
        public string? Notes { get; set; }
    }

    public class BulkConvertToLeadRequestHandler : IRequestHandler<BulkConvertToLeadRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _masterLeadStatusRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.LeadHistory> _leadHistoryRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IUserService _userService;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;

        public BulkConvertToLeadRequestHandler(
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            ICurrentUser currentUser,
            //IRepositoryWithEvents<MasterLeadStatus> masterLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.LeadHistory> leadHistoryRepo,
            INotificationSenderService notificationSenderService,
            IUserService userService,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo

            )
        {
            _prospectRepo = prospectRepo;
            _leadRepo = leadRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _currentUser = currentUser;
            //_masterLeadStatusRepo = masterLeadStatusRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _notificationSenderService = notificationSenderService;
            _userService = userService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _propertyTypeRepo = propertyTypeRepo;
            _customLeadStatusRepo = customLeadStatusRepo;
            _projectRepo = projectRepo;
        }
        public async Task<Response<bool>> Handle(BulkConvertToLeadRequest request, CancellationToken cancellationToken)
        {
            if (!(request?.Ids?.Any() ?? false))
            {
                throw new InvalidOperationException("Provide Ids to Convert To Lead");
            }
            var existingProspects = await _prospectRepo.ListAsync(new GetProspectByIdsSpecs(request.Ids), cancellationToken);
            var users = await _userService.GetListAsync(cancellationToken);
            var AssignedUsers = users.Where(i => request.AssignTo.Contains(i.Id)).ToList();
            int i = 0;
            if (existingProspects?.Any() ?? true)
            {
                foreach (var prospect in existingProspects)
                {
                    var lead = CreateLeadFromProspect(prospect);
                    if (!string.IsNullOrEmpty(request.Notes))
                    {
                        lead.Notes = request.Notes;
                    }
                    var currentUserId = _currentUser.GetUserId();

                    #region Status

                    var newStatus = (await _customLeadStatusRepo.ListAsync(cancellationToken));

                    if (request.LeadStatusId != null && request.LeadStatusId != Guid.Empty)
                    {
                        var customLeadStatus = newStatus.Where(i => i.Id == request.LeadStatusId).FirstOrDefault();
                        if (customLeadStatus?.Level < 1)
                        {
                            throw new ArgumentException("Provide Child Id of this Status");
                        }
                        lead.CustomLeadStatus = customLeadStatus ?? newStatus?.FirstOrDefault(i => i.IsDefault) ?? newStatus?.FirstOrDefault(i => i.Status == "new");
                        lead.ScheduledDate = request.ScheduledDate;
                    }
                    else
                    {
                        lead.CustomLeadStatus = newStatus?.FirstOrDefault(i => i.IsDefault) ?? newStatus?.FirstOrDefault(i => i.Status == "new");
                    }
                    #endregion

                    #region Projects
                    List<Lrb.Domain.Entities.Project> tempProjects = new();
                    request.Projects = (request.Projects?.Any() ?? false) ? request.Projects.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                    if (request.Projects?.Any() ?? false)
                    {
                        foreach (var newProject in request.Projects)
                        {
                            Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetProjectByIdSpecsV2(newProject), cancellationToken)).FirstOrDefault();
                            if (existingProject != null)
                            {
                                tempProjects.Add(existingProject);
                            }
                            else
                            {
                                Domain.Entities.Project tempProject = new() { Name = newProject };
                                tempProject = await _projectRepo.AddAsync(tempProject, cancellationToken);
                                tempProjects.Add(tempProject);
                            }
                        }
                        lead.Projects = tempProjects;
                    }
                    #endregion



                    lead.TagInfo = new LeadTag();
                    var enquiry = prospect?.Enquiries?.Where(i => i.IsPrimary).FirstOrDefault();
                    MasterPropertyType? propertyType = null;
                    if (enquiry?.PropertyType != null)
                    {
                        propertyType = await _propertyTypeRepo.GetByIdAsync(enquiry.PropertyType.Id, cancellationToken);
                        if (propertyType == null)
                        {
                            throw new InvalidDataException("Property type Id does not belong to master data");
                        }
                    }
                    List<MasterPropertyType>? propertyTypes = null;
                    if (enquiry?.PropertyTypes != null)
                    {
                        propertyTypes = await _propertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(enquiry.PropertyTypes.Select(i => i.Id).ToList()));
                    }

                    if (enquiry != null)
                    {
                        var leadEnquiry = CreateLeadEnquiryFromProspectEnquiry(enquiry);
                        leadEnquiry.PropertyType = propertyType;
                        leadEnquiry.PropertyTypes = propertyTypes;
                        lead.Enquiries = new List<LeadEnquiry>();
                        lead.Enquiries.Add(leadEnquiry);
                    }
                    UserDetailsDto user = new();
                    if (!(request.AssignTo.Any(i => i == Guid.Empty)))
                    {
                        user = AssignedUsers[i];
                    }
                    lead.AssignTo = user.Id;
                    //lead.AssignedFrom = _currentUser.GetUserId();
                    try
                    {
                        Guid? parentLeadId = null;
                        var rootLead = await _leadRepo.FirstOrDefaultAsync(new Lrb.Application.DataManagement.Mobile.Specs.GetContcactNoSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                        if (rootLead != null)
                        {
                            lead.RootId = rootLead.Id;
                            lead.DuplicateLeadVersion = "D" + (rootLead.ChildLeadsCount + 1);
                            lead.ParentLeadId = parentLeadId != null ? parentLeadId : rootLead.Id;
                            rootLead.ChildLeadsCount += 1;
                            try
                            {
                                await _leadRepo.UpdateAsync(rootLead);
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "ConvertToLeadRequestHander -> Handle() -> UpdateParentLead()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "ConvertToLeadRequestHander -> Handle() -> ScheduleNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    try
                    {
                        lead = await _leadRepo.AddAsync(lead);

                        prospect.IsQualified = true;
                        prospect.IsConvertedToLead = true;
                        prospect.ConvertedBy = currentUserId;
                        prospect.ConvertedDate = DateTime.UtcNow;
                        if (prospect.QualifiedDate != null)
                        {
                            prospect.QualifiedDate = DateTime.UtcNow;
                        }
                        if (prospect.QualifiedBy != null)
                        {
                            prospect.QualifiedBy = currentUserId;
                        }

                        await _prospectRepo.UpdateAsync(prospect);
                        i++;
                        if (i == AssignedUsers.Count)
                            i = 0;
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "ConvertToLeadRequestHander -> Handle() -> Add Lead()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    var leadDto = lead.Adapt<ViewLeadDto>();
                    await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                    var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                    try
                    {
                        await _leadHistoryRepo.AddAsync(leadHistory);
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "ConvertToLeadRequestHander -> Handle() -> Add History()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    if (lead.AssignTo != default && lead.AssignTo != Guid.Empty && lead.AssignTo != _currentUser.GetUserId())
                    {
                        try
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, leadDto.AssignedUser?.Name, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, status: leadDto.Status?.DisplayName);
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "ConvertToLeadRequestHander -> Handle() -> ScheduleNotificationsAsync()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                }
            }
            return new(true);
        }

        public Lrb.Domain.Entities.Lead CreateLeadFromProspect(Prospect prospect)
        {
            Lrb.Domain.Entities.Lead lead = new();
            lead.Name = prospect?.Name?.Trim() ?? string.Empty;
            lead.ContactNo = prospect?.ContactNo?.Trim() ?? string.Empty;
            lead.AlternateContactNo = prospect?.AlternateContactNo?.Trim();
            lead.Notes = prospect?.Notes;
            lead.Email = prospect?.Email;
            lead.Address = new Address()
            {
                SubLocality = prospect?.Address?.SubLocality,
                Locality = prospect?.Address?.Locality,
                PlaceId = prospect?.Address?.PlaceId,
                City = prospect?.Address?.City,
                District = prospect?.Address?.District,
                State = prospect?.Address?.State,
                Country = prospect?.Address?.Country,
                TowerName = prospect?.Address?.TowerName,
                Community = prospect?.Address?.Community,
                SubCommunity = prospect?.Address?.SubCommunity,
            };
            lead.LeadNumber = lead?.Name?[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
            lead.AgencyName = prospect?.AgencyName?.Trim();
            lead.Agencies = prospect?.Agencies;
            lead.Designation = prospect?.Designation;
            lead.ChannelPartners = prospect?.ChannelPartners;
            lead.ClosingManager = prospect?.ClosingManager;
            lead.SourcingManager = prospect?.SourcingManager;
            lead.ContactRecords = prospect?.ContactRecords;
            lead.CompanyName = prospect?.CompanyName;
            lead.Properties = prospect?.Properties;
            lead.Projects = prospect?.Projects;
            lead.Profession = prospect?.Profession ?? Profession.None;
            lead.ReferralName=prospect?.ReferralName;      
            lead.ReferralContactNo=prospect?.ReferralContactNo;
            lead.ReferralEmail=prospect?.ReferralEmail;
            lead.Nationality = prospect?.Nationality;
            lead.OriginalOwner = prospect?.AssignTo;
            lead.LandLine = prospect?.LandLine;
            lead.PossesionType = prospect?.Enquiries?.FirstOrDefault()?.PossesionType;
            lead.CountryCode = prospect?.CountryCode;
            lead.AltCountryCode = prospect?.AltCountryCode;

            return lead;
        }

        #region Create Lead Enquiry

        public Lrb.Domain.Entities.LeadEnquiry CreateLeadEnquiryFromProspectEnquiry(ProspectEnquiry enquiry)
        {
            var leadEnquiry = new LeadEnquiry();
            LeadSource? source = EnumFromDescription.GetValueFromDescription<LeadSource>(enquiry?.Source.DisplayName ?? string.Empty);
            leadEnquiry.LeadSource = source ?? LeadSource.Direct;
            leadEnquiry.IsPrimary = true;
            var address = new Address()
            {
                SubLocality = enquiry?.Address?.SubLocality,
                Locality = enquiry?.Address?.Locality,
                PlaceId = enquiry?.Address?.PlaceId,
                City = enquiry?.Address?.City,
                District = enquiry?.Address?.District,
                State = enquiry?.Address?.State,
                Country = enquiry?.Address?.Country,
                TowerName = enquiry?.Address?.TowerName,
                Community = enquiry?.Address?.Community,
                SubCommunity = enquiry?.Address?.SubCommunity,

            };
            leadEnquiry.BHKs = enquiry?.BHKs;
            leadEnquiry.BHKTypes = enquiry?.BHKTypes;
            leadEnquiry.EnquiryTypes = enquiry?.EnquiryTypes;
            leadEnquiry.EnquiredFor = enquiry?.EnquiryType ?? EnquiryType.None;
            leadEnquiry.SubSource = enquiry?.SubSource?.ToLower();
            leadEnquiry.LowerBudget = enquiry?.LowerBudget;
            leadEnquiry.UpperBudget = enquiry?.UpperBudget;
            leadEnquiry.CarpetArea = enquiry?.CarpetArea;
            leadEnquiry.CarpetAreaInSqMtr = enquiry?.CarpetAreaInSqMtr;
            leadEnquiry.CarpetAreaUnitId = enquiry?.CarpetAreaUnitId ?? Guid.Empty;
            leadEnquiry.NoOfBHKs = enquiry?.NoOfBhks ?? default;
            leadEnquiry.Address = address;
            leadEnquiry.BuiltUpArea = enquiry?.BuiltUpArea;
            leadEnquiry.BuiltUpAreaUnitId = enquiry?.BuiltUpAreaUnitId ?? Guid.Empty;
            leadEnquiry.BuiltUpAreaInSqMtr = enquiry?.BuiltUpAreaInSqMtr;
            leadEnquiry.SaleableArea = enquiry?.SaleableArea;
            leadEnquiry.SaleableAreaInSqMtr = enquiry?.SaleableAreaInSqMtr;
            leadEnquiry.SaleableAreaUnitId = enquiry?.SaleableAreaUnitId ?? Guid.Empty;
            leadEnquiry.Beds = enquiry?.Beds;
            leadEnquiry.Baths = enquiry?.Baths;
            leadEnquiry.Floors = enquiry?.Floors;
            leadEnquiry.Furnished = enquiry?.Furnished;
            leadEnquiry.OfferType = enquiry?.OfferType;
            leadEnquiry.NetArea = enquiry?.NetArea;
            leadEnquiry.NetAreaInSqMtr = enquiry?.NetAreaInSqMtr;
            leadEnquiry.NetAreaUnitId = enquiry?.NetAreaUnitId ?? Guid.Empty;
            leadEnquiry.PropertyArea = enquiry?.PropertyArea;
            leadEnquiry.PropertyAreaInSqMtr = enquiry?.PropertyAreaInSqMtr;
            leadEnquiry.PropertyAreaUnitId = enquiry?.PropertyAreaUnitId ?? Guid.Empty;
            leadEnquiry.UnitName = enquiry?.UnitName;
            leadEnquiry.ClusterName = enquiry?.ClusterName;
            leadEnquiry.Purpose= enquiry?.Purpose;
            leadEnquiry.PropertyTypes = enquiry?.PropertyTypes;



            if (enquiry?.Addresses?.Any() ?? false)
            {
                var addresses = new List<Address>();
                foreach (var item in enquiry.Addresses)
                {
                    addresses.Add(new Address()
                    {
                        SubLocality = item?.SubLocality,
                        Locality = item?.Locality,
                        PlaceId = item?.PlaceId,
                        City = item?.City,
                        District = item?.District,
                        State = item?.State,
                        Country = item?.Country,
                        TowerName = item?.TowerName,
                        Community = enquiry?.Address?.Community,
                        SubCommunity = enquiry?.Address?.SubCommunity,
                    });
                }
                leadEnquiry.Addresses = addresses;
            }
            return leadEnquiry;
        }

        #endregion

    }
}
