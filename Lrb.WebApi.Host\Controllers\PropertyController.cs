﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.GlobalSettings.Web.Dto;
using Lrb.Application.GlobalSettings.Web.Requests;
using Lrb.Application.Lead.Web.Requests.GetRequests;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Dtos.XMl_Feed;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.Property.Web.Requests.Microsite;
using Lrb.Application.Property.Web.Requests.XML_Feed;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Request;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Mapster;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Serialization;
using static Lrb.Application.Property.Web.Requests.Microsite.GetAllSimilarPropertyRequestHandler;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class PropertyController : VersionedApiController
    {
        private readonly IRepository<Property> _repository;
        private readonly IRepository<Address> _addressRepo;
        private readonly IRepository<MasterPropertyType> _masterPrRepo;
        private readonly Serilog.ILogger _logger;
        public PropertyController(IRepository<Property> repository, IRepository<Address> addressRepo, IRepository<MasterPropertyType> masterPrRepo, Serilog.ILogger logger)
        {
            _repository = repository;
            _addressRepo = addressRepo;
            _masterPrRepo = masterPrRepo;
            _logger = logger;
        }
        [HttpGet]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public Task<PagedResponse<ViewPropertyDto, PropertyCountDto>> SearchAsync([FromQuery] GetAllPropertyRequest request)
        {
            return Mediator.Send(request);
        }
        [AllowAnonymous]
        [HttpGet("anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all properties.", "")]
        public  Task<PagedResponse<PullViewPropertyDto, PropertyCountDto>> GetAsync([FromQuery] GetAllPropertyAnonymousRequest request)
        {
            if (HttpContext.Request.Headers.TryGetValue(Lrb.Shared.Multitenancy.MultitenancyConstants.TenantIdName, out var tenantIdValues))
            {
                string? tenantId = tenantIdValues.FirstOrDefault();
                _logger.Information("GetAllPropertyAnonymousRequest info: PageNumber={PageNumber}, PageSize={PageSize}, TenantId={TenantId}", request.PageNumber, request.PageSize, tenantId);

            }
            return Mediator.Send(request);
        }
        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get property details.", "")]
        public Task<Response<ViewPropertyDto>> GetAsync(Guid id)
        {
            return Mediator.Send(new GetProrpertyRequest(id));
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Create a new property.", "")]
        public Task<Response<UpdatePropertyDto>> CreateAsync([FromBody] CreatePropertyDto dto)
        {
            CreatePropertyRequest request = dto.Adapt<CreatePropertyRequest>();
            return Mediator.Send(request);
        }

        [HttpPut("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update a property.", "")]
        public async Task<ActionResult<Guid>> UpdateAsync(UpdatePropertyDto dto, Guid id)
        {
            return id != dto.Id
                ? BadRequest()
                : Ok(await Mediator.Send(dto.Adapt<UpdatePropertyRequest>()));
        }

        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Properties)]
        [OpenApiOperation("Delete a property.", "")]
        public Task<Response<Guid>> DeleteAsync(Guid id)
        {
            return Mediator.Send(new DeletePropertyByIdRequest(id));
        }

        [HttpPut("status/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update status of a property.", "")]
        public async Task<Response<bool>> UpdateStatusAsync(Guid id)
        {
            return await Mediator.Send(new UpdatePropertyStatusRequest(id));
        }


        [HttpPut("shareCount")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.BulkShare, LrbResource.Properties)]
        [OpenApiOperation("Update shareCount of a property.", "")]
        public async Task<ActionResult<bool>> UpdateShareCountAsync([FromBody] UpdatePropertyShareCountRequest request)
        {
            return Ok(await Mediator.Send(request));
        }

        [HttpPut("tagInfo/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update tagInfo of a property.", "")]
        public async Task<ActionResult<bool>> UpdateTagInfoAsync(UpdatePropertyTagInfoRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPost("BasicInfo")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Create a new property.", "")]
        public Task<Response<Guid>> CreateAsync([FromBody] CreateBasicPropertyInfoRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("PropertiesInfo")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Create a new property.", "")]
        public Task<Response<Guid>> CreateAsync([FromBody] CreatePropertiesInfoRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPut("Gallery{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Update Gallery of a Property.", "")]
        public async Task<ActionResult<Guid>> CreateAsync([FromBody] UpdatePropertyGalleryRequest request, Guid id)
        {
            return id != request.PropertyId
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPost("Attributes")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Create a new property.", "")]
        public Task<Response<Guid>> CreateAsync([FromBody] CreatePropertyAttributesRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("Amenities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Create a new property.", "")]
        public Task<Response<Guid>> CreateAsync([FromBody] CreatePropertyAmenitiesRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("FilterData")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get Data for Filter Dropdown.", "")]
        public async Task<PagedResponse<FilterDropdownDataDto, string>> GetFilterDataAsync([FromQuery] GetFilterDropdownDataRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("GalleryDropdownData")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get Data Gallery Dropdown.", "")]
        public async Task<PagedResponse<string, string>> GetGalleryDropdownDataAsync([FromQuery] GetGalleryDropdownDataRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("Archive/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Archive/Restore a property.", "")]
        public async Task<Response<bool>> ArchivePropertyAsync(Guid id)
        {
            return await Mediator.Send(new ArchivePropertyRequest(id));
        }

        [HttpPut("Brochures/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update Brochures.", "")]
        public async Task<ActionResult<bool>> UpdateBrochuresAsync(UpdateBrochureRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }

        [HttpGet("Brochures/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get Brochures.", "")]
        public Task<List<BrochureDto>> GetBrochuresAsync(Guid id)
        {
            return Mediator.Send(new GetBrochuresRequest(id));
        }
        [HttpGet("matchingLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Matching Leads.", "")]
        public async Task<PagedResponse<LeadWithDegreeMatched, string>> GetMatchingLeadsAsync([FromQuery] GetMatchingLeadsByPropertyIdRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("archived")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get Archived Properties.", "")]
        public async Task<PagedResponse<ViewPropertyDto, PropertyCountDto>> GetArchivedProperties([FromQuery] GetArchivedPropertiesRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("excel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Properties)]
        [OpenApiOperation("Upload excel File")]
        public async Task<ActionResult<Response<Application.Property.Web.FileColumnDto>>> UploadExcelFileAsync(IFormFile file)
        {
            try
            {
                return await Mediator.Send(new GetExcelColumnsUsingEPPlusRequest(file));
            }
            catch (Exception e)
            {
                _logger.Error("PropertyController -> UploadExcelFileAsync, Error: " + JsonConvert.SerializeObject(e));
                throw;
            }

        }
        [HttpPost("batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Properties)]
        [OpenApiOperation("Create new properties by excel.", "")]
        public Task<Response<BulkPropertyUploadTracker>> CreateBulkAsync(RunAWSBatchForBulkPropertyUploadRequest request)
        {
            _logger.Information("PropertyController -> createLeadRequest, File: " + JsonConvert.SerializeObject(request));
            return Mediator.Send(request);
        }

        [HttpGet("bulk/trackers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Properties)]
        [OpenApiOperation("Get all bulk property upload trackers", "")]
        public async Task<PagedResponse<BulkPropertyUploadTracker, string>> GetAllTrackers([FromQuery] GetAllBulkUploadTrackersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("ownernames")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get owner names in properties", "")]
        public async Task<Response<List<string>>> GetAgencyAsync()
        {
            return await Mediator.Send(new GetOwnerNamesRequest());
        }

        [HttpGet("addresses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property address.", "")]
        public async Task<Response<List<string>>> GetAsync()
        {
            return await Mediator.Send(new GetAllPropertyAddressRequest());
        }
        [HttpGet("idwithname")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property ids and names.", "")]
        public async Task<Response<List<PropertiesWithIds>>> GetIdsWithNamesAsync()
        {
            return await Mediator.Send(new GetAllPropertiesByIdAndNamesRequest());
        }

        #region Microsite

        [AllowAnonymous]
        [HttpGet("microsite")]
        [TenantIdHeader]
        [OpenApiOperation("Get Property for Microsite", "")]
        public async Task<Response<PropertyMicrositeDto>> GetPropertyForMicrosite(string serialNo)
        {
            return await Mediator.Send(new GetPropertyByIdForMicrositeRequest(serialNo));
        }
        [AllowAnonymous]
        [HttpGet("microsite/anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get Anonymous Property for Microsite", "")]
        public async Task<Response<PullPropertyMicrositeDto>> GetPropertyForMicrositeAsync(string serialNo)
        {
            return await Mediator.Send(new GetPropertyByIdForMicrositeAnonymousRequest(serialNo));
        }
        [AllowAnonymous]
        [HttpGet("microsite/similar-properties")]
        [TenantIdHeader]
        [OpenApiOperation("Get Similar Properties for Microsite", "")]
        public async Task<PagedResponse<PropertyWithDegreeMatched, string>> GetSimilarPropertyAsync([FromQuery] GetAllSimilarPropertyRequest request)
        {
            try
            {
                var tenantId = this.HttpContext.Request.Headers["tenant"];
                // var request = new GetAllSimilarPropertyRequest();
                request.TenantId = tenantId;
                return await Mediator.Send(request);
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }

        [AllowAnonymous]
        [HttpPost("microsite/lead")]
        [TenantIdHeader]
        [OpenApiOperation("Create Lead From Microsite", "")]
        public async Task<Response<bool>> CreateLeadFromMicrosite([FromBody] CreateMicrositeLeadRequest request)
        {
            return await Mediator.Send(request);
        }

        [AllowAnonymous]
        [HttpGet("microsite/user")]
        [TenantIdHeader]
        [OpenApiOperation("Get User User Details")]
        public async Task<Response<MicrositeUserDto>> GetUserDetailsMicrosite(string userName)
        {
            return await Mediator.Send(new GetUserDetailsForMicrositeRequest(userName));
        }
        [HttpPost("export/batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Properties)]
        [OpenApiOperation("export properties by excel.", "")]
        public Task<Response<Guid>> ExportLeadsAsync(RunAWSBatchForExportPropertiesRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("export/trackers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all export properties trackers", "")]
        public async Task<PagedResponse<ExportPropertyTrackerDto, string>> GetAllTrackers([FromQuery] GetPropertyExportTracker request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpGet("lead-count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get lead count for proeprties by ids.", "")]
        public async Task<Response<IEnumerable<PropertyLeadCountDto>>> GetAsync([FromQuery] GetLeadsCountByPropertyIdsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("UpdatePropertyAssignedTo/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Properties)]
        [OpenApiOperation("Update AssignedTo of a Property", "")]
        public async Task<ActionResult<bool>> UpdateAsync(UpdatePropertyAssignedToRequest request, Guid id)
        {
            return id != request.PropertyId
                 ? BadRequest()
                 : Ok(await Mediator.Send(request));
        }
        [HttpPut("AssignToMultipleProperties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkReassign, LrbResource.Properties)]
        [OpenApiOperation("Assign properties to users.", "")]
        public async Task<Response<bool>> AssignPropertiessAsyncV2(PropertiesUserAssignmentsRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpGet("Currency")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get All Currency .", "")]
        public async Task<Response<List<string>>> GetAllCuurencyy()
        {
            return await Mediator.Send(new GetAllPrpertyCurrencyRequest());
        }
        [HttpDelete("SoftdeleteProperties")]
        [TenantIdHeader]
        [OpenApiOperation("Delete Properties")]
        public async Task<ActionResult<Response<bool>>> SoftDeleteAsync(SoftDeletePropertyRequest request)
        {
            return Ok(await Mediator.Send(request));
        }
        [HttpPut("RestoreDeletedProperties")]
        [TenantIdHeader]
        [OpenApiOperation("Restore Deleted Properties")]

        public async Task<ActionResult<Response<bool>>> RetriveDeletedPropertiesAsync(RestoreDeletedPropertyRequest request)
        {
            return Ok(await Mediator.Send(request));
        }
        [HttpPost("image/upload")]
        [AllowAnonymous]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("upload image")]
        public async Task<IActionResult> UploadImageAsync([FromQuery] UploadImageRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);

                if (response.Succeeded)
                {
                    return Ok(new { imageUrl = response.Data });
                }
                else
                {
                    return BadRequest(new { message = string.Join(", ", response.Errors) });
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        [AllowAnonymous]
        [HttpGet("SerialNumbers/{tenant}")]
        [OpenApiOperation("Get All Property Microsite Serial Numbers", "")]
        public async Task<Response<List<string>>> GetAllMicrosites(string tenant)
        {
            return await Mediator.Send(new GetAllMicrositeSerialNumbersRequest(tenant));
        }

        [AllowAnonymous]
        [HttpGet("microsite/similar-properties/new")]
        [TenantIdHeader]
        [OpenApiOperation("Get Similar Properties for Microsite", "")]
        public async Task<PagedResponse<PropertyWithDegreeMatchedV2, string>> GetSimilarPropertyV2Async([FromQuery] V2GetAllSimilarPropertyRequest request)
        {
            var tenantId = this.HttpContext.Request.Headers["tenant"];
            request.TenantId = tenantId;
            return await Mediator.Send(request);
        }


        [HttpGet("new/all")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public Task<PagedResponse<ViewPropertyDto, string>> SearchAsync([FromQuery] V2GetAllPropertyRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public Task<Response<PropertyCountDto>> SearchAsync([FromQuery] V2GetAllPropertyCountRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPut("applyWaterMark{id:guid}")]
        [AllowAnonymous]
        [OpenApiOperation("Update watermark for property and project images.", "")]
        public async Task<ActionResult<Guid>> CreateAsync([FromBody] UpdateWaterMarkPropertyGalleryRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpGet("assignments")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get  property Assignmennts.", "")]
        public Task<Response<PropertyUserAssignmentDto>> GetAssignmentsAsync([FromQuery] GetPropertyAssignmentRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("multiplePropertiesByIds")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get multiple property details by IDs", "")]
        public Task<Response<List<ViewPropertyDto>>> GetMultipleAsync([FromBody] GetMultiplePropertiesRequest request)
        {
            return Mediator.Send(request);
        }
        #region Property Listing
        [AllowAnonymous]
        [HttpGet("xmlfeed/{tenant}")]
        [Produces("application/xml")]
        [OpenApiOperation("Get All Property in XML", "")]
        public async Task<IActionResult> GetAll(string tenant)
        {
            var data = await Mediator.Send(new PFPropertyListingXMLFeedRequest() { TenantId = tenant });
            var serializer = new XmlSerializer(typeof(PropertyList));
            var xmlNamespaces = new XmlSerializerNamespaces();
            xmlNamespaces.Add("", ""); // Add the default namespace if needed

            using (var stringWriter = new StringWriter())
            {
                // Create an XmlWriterSettings to disable the encoding declaration
                var settings = new XmlWriterSettings
                {
                    OmitXmlDeclaration = true, // This will omit the XML declaration (encoding)
                    Indent = true // Optional: Indentation for better readability
                };

                // Use XmlWriter to write the XML without the declaration
                using (var xmlWriter = XmlWriter.Create(stringWriter, settings))
                {
                    serializer.Serialize(xmlWriter, data, xmlNamespaces);
                }

                var xmlResult = stringWriter.ToString();
                return Content(xmlResult, "application/xml");
            }
        }

        [HttpGet("all/listing")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public async Task<PagedResponse<ViewListingManagementDto, string>> SearchAsync([FromQuery] GetAllPropertyForListingManagementRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("listing/top-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property top level count.", "")]
        public async Task<Response<GetPropertyCountForListingManagementDto>> CountAsync([FromQuery] GetPropertyBaseLevelCountForListingManagementRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("listing/base-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property base level count.", "")]
        public async Task<Response<GetPropertySecondLevelFilterCountForListingManagementDto>> CountAsync([FromQuery] GetPropertySecondLevelCountForListingRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("publish")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.PublishProperty, LrbResource.Properties)]
        [OpenApiOperation("send properties for listing.", "")]
        public async Task<Response<bool>> ListPropertyAsync(SendPropertyForListingApprovalRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("delist")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkDeList, LrbResource.Properties)]
        [OpenApiOperation("delist properties from listing.", "")]
        public async Task<Response<bool>> DelistPropertyAsync(DelistPropertyFromPortalRequest request)
        {
            return await Mediator.Send(request);
        }

        [AllowAnonymous]
        [HttpGet("listing/microsite")]
        [TenantIdHeader]
        [OpenApiOperation("Get Property microsite for listing management")]
        public async Task<Response<PropertyMicrositeDtoListingManagement>> GetMicrositeAsync(string serialNo)
        {
            return await Mediator.Send(new GetPropertyMicrositeForListingBySerialNoRequest(serialNo));
        }
        [HttpGet("listing/matchingLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Matching Leads.", "")]
        public async Task<PagedResponse<LeadWithDegreeMatched, string>> GetMatchingLeadsAsync([FromQuery] GetMatchingLeadsByPropertyIdForListingRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("listing/export/batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Properties)]
        [OpenApiOperation("export properties by excel.", "")]
        public Task<Response<Guid>> ExportPropertyListingAsync(RunAWSBatchForExportPropertiesForListingManagementRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpDelete("Delete")]
        [TenantIdHeader]
        [OpenApiOperation("Bulk property delete")]
        public async Task<Response<bool>> PermanantPropertyDeleteRequest([FromBody] PermanantPropertyDeleteRequest request)
        {
            return await Mediator.Send(request);
        }
        
        [AllowAnonymous]
        [HttpGet("all/listing/anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all property details.", "")]
        public async Task<PagedResponse<PullViewPropertyForListingDto, string>> SearchAsync([FromQuery] GetAllPropertyAnonymousForListingRequest request)
        {
            if (HttpContext.Request.Headers.TryGetValue(Lrb.Shared.Multitenancy.MultitenancyConstants.TenantIdName, out var tenantIdValues))
            {
                string? tenantId = tenantIdValues.FirstOrDefault();
                _logger.Information("GetAllPropertyAnonymousForListingRequest info: PageNumber={PageNumber}, PageSize={PageSize}, TenantId={TenantId}", request.PageNumber, request.PageSize, tenantId);

            }
            return await Mediator.Send(request);
        }

        [HttpPost("cloneProperty")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.CloneProperty, LrbResource.ListingIntegration)]
        [OpenApiOperation("Create a Clone property.", "")]
        public async Task<ActionResult<Guid>> CreateAsync(CreateClonePropertyRequest request)
        {
            var result = await Mediator.Send(request);
            return Ok(result);
        }
        #endregion

        [HttpGet("all/properties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all lead properties.", "")]
        public async Task<Response<List<UserPropertyInfoDto>>> GetAllPropertiesAsync()
        {
            return await Mediator.Send(new GetAllPropertyInfoRequest());
        }

        [HttpPost("import/xml-feed")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Import properties from external XML feed", "")]
        public async Task<Response<ImportRealtyXmlFeedResponseDto>> ImportFromXmlFeedAsync([FromBody] ImportRealtyXmlFeedRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion
    }
}

