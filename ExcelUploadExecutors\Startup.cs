﻿using AWS.Logger;
using AWS.Logger.SeriLog;
using Dapper;
using Finbuckle.MultiTenant;
using Lrb.Application;
using Lrb.Application.Common.Persistence;
using Lrb.Infrastructure;
using Lrb.Infrastructure.Multitenancy;
using Lrb.Infrastructure.Persistence;
using Lrb.Infrastructure.Persistence.Repository;
using Lrb.Shared.Logger;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Hosting.Internal;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Npgsql;
using Serilog;
using System;
using System.IO;

namespace LrbExcelUpload
{
    public class Startup
    {
        private readonly IConfigurationRoot Configuration;
        public Startup(string environmentName)
        {
            //var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "dev"; //value comming from lambda environment variable
            Console.WriteLine($"Environment: {environmentName}");
            Configuration = new ConfigurationBuilder() // ConfigurationBuilder() method requires Microsoft.Extensions.Configuration NuGet package
                .SetBasePath(Directory.GetCurrentDirectory())  // SetBasePath() method requires Microsoft.Extensions.Configuration.FileExtensions NuGet package
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true) // AddJsonFile() method requires Microsoft.Extensions.Configuration.Json NuGet package
                .AddJsonFile($"appsettings.{environmentName}.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables() // AddEnvironmentVariables() method requires Microsoft.Extensions.Configuration.EnvironmentVariables NuGet package
                .AddConfigurations(environmentName)
                .Build();
        }

        public IServiceProvider ConfigureServices(string tenantId, string env)
        {
            var services = new ServiceCollection(); // ServiceCollection require Microsoft.Extensions.DependencyInjection NuGet package

            ConfigureLoggingAndConfigurations(services);

            ConfigureApplicationServices(services, env);
            #region Logger
            AWSLoggerConfig configuration = new()
            {
                Region = Configuration["Serilog:Region"],
                Credentials = new SerilogAWSCredentials(),
                LogGroup = Configuration["Serilog:LogGroup"],
            };

            //Initialize Logger
            var logger = Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(Configuration)
                .WriteTo.AWSSeriLog(configuration)
                .CreateLogger();

            #endregion
            services.AddSingleton<Serilog.ILogger>(logger);
            services.AddInfrastructureForLambda(Configuration, tenantId);
            var (connectionString, readReplicaConnectionString) = GetConnectionString(services, tenantId);
            var tenantInfo = new LrbTenantInfo() { Id = tenantId, Identifier = tenantId, Name = tenantId, ConnectionString = connectionString, ReadReplicaConnectionString = readReplicaConnectionString};
            services.AddSingleton<ITenantInfo>(tenantInfo);
            IServiceProvider provider = services.BuildServiceProvider();
            if (tenantInfo is not null)
            {
                provider.GetRequiredService<IMultiTenantContextAccessor>()
                    .MultiTenantContext = new MultiTenantContext<LrbTenantInfo>
                    {
                        TenantInfo = tenantInfo
                    };
            }
            provider.ConfigureMappings();
            provider.Conig();
            return provider;
        }
        public (string ConnectionString, string ReadReplicaConnectionString) GetConnectionString(IServiceCollection services, string tenantId)
        {
            try
            {
                var defaultConnection = (services?.BuildServiceProvider()?.GetService<IOptions<DatabaseSettings>>()?.Value.ConnectionString) ?? string.Empty;
                var defaultReplicaConnection = (services?.BuildServiceProvider()?.GetService<IOptions<DatabaseSettings>>()?.Value.ReadReplicaConnectionString) ?? string.Empty;
                var conn = new NpgsqlConnection(defaultConnection);
                try
                {
                    conn.Open();
                    var result = conn.QueryFirstOrDefault<(string ConnectionString, string ReadReplicaConnectionString)>($"SELECT \"ConnectionString\", \"ReadReplicaConnectionString\" FROM \"MultiTenancy\".\"Tenants\" WHERE \"Id\" = '{tenantId}'");
                    return (
                     string.IsNullOrEmpty(result.ConnectionString) ? defaultConnection : result.ConnectionString,
                     string.IsNullOrEmpty(result.ReadReplicaConnectionString) ? defaultReplicaConnection : result.ReadReplicaConnectionString);
                }
                catch (Exception e)
                {
                    return (defaultConnection, defaultReplicaConnection);
                }

                finally
                {
                    conn.Close();
                }
            }
            catch (Exception ex)
            {
                //ignore
            }
            return (string.Empty, string.Empty);
        }
        private void ConfigureLoggingAndConfigurations(ServiceCollection services)
        {

            // Add configuration service
            services.AddSingleton<IConfiguration>(Configuration);
            AWSLoggerConfig configuration = new()
            {
                Region = Configuration["Serilog:Region"],
                Credentials = new SerilogAWSCredentials(),
                LogGroup = Configuration["Serilog:LogGroup"],
            };
            var logger = Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(Configuration)
                .WriteTo.AWSSeriLog(configuration)
                .CreateLogger();

            // Add logging service
            services.AddLogging(loggingBuilder =>  // AddLogging() requires Microsoft.Extensions.Logging NuGet package
            {
                loggingBuilder.ClearProviders();
                loggingBuilder.AddConsole();
                loggingBuilder.AddSerilog(logger);// AddConsole() requires Microsoft.Extensions.Logging.Console NuGet package
            });

            //services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly()).AddMediatR(Assembly.GetEntryAssembly());
            services.AddApplication();
            services.AddOptions<DatabaseSettings>()
                .BindConfiguration(nameof(DatabaseSettings));
        }

        private void ConfigureApplicationServices(ServiceCollection services, string env)
        {
            services.AddSingleton<ITenantIndependentRepository, TenantIndependentRepository>();
            services.AddSingleton<ExcelUpload.IFunctionEntryPoint, ExcelUpload.FunctionEntryPoint>();
            services.AddSingleton<IHostEnvironment>(new HostingEnvironment { EnvironmentName = env });
        }
    }
}
