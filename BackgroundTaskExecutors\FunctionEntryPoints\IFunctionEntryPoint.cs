﻿using BackgroundTaskExecutors.DTOs;
using Lrb.Application.Attendance.Web.Requests;
using Lrb.Application.CustomEmail.Web;
using Lrb.Application.Lead.Mobile.Requests;
using Lrb.Application.Lead.Mobile.v2;
using Lrb.Application.Lead.Web.Requests;
using System;
using System.Threading.Tasks;

namespace BackgroundTaskExecutors
{
    public interface IFunctionEntryPoint
    {
        Task ScheduleNotificationsAsync(SendNotificationDto input);
        Task UpdateLeadHistoryAsync(LeadHistoryDto input);
        Task AssignLeadsBasedOnScenariosAsync(V2AssignLeadsBasedOnScenariosRequest input);
        Task SeedAttendanceSettingAsync(AttendanceSettingObject input, Guid? currentUserId, string tenantId);
        Task AssignPFPropertyDetailsAsync(string tenantId, LrbAssignPfPropertyDto lrbDto);
    }
}
