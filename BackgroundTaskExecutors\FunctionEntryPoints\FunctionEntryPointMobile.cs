﻿using Lrb.Application.Lead.Mobile.Requests;
using Lrb.Application.Lead.Mobile.Requests.UpdateStatusHandler;
using Lrb.Application.Lead.Mobile.v2;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace BackgroundTaskExecutors
{
    public class FunctionEntryPointMobile : LeadCommonRequestHandler, IFunctionEntryPointMobile
    {
        public FunctionEntryPointMobile(IServiceProvider serviceProvider) : base(serviceProvider, typeof(FunctionEntryPointMobile).Name, "Function")
        {
        }
        public async Task UpdateLeadStatusAsync(V2UpdateLeadStatusRequest input, Guid? currentUserId,string? tenantId = null)
        {
            try
            {
                await V2UpdateLeadStatusAsync(input, currentUserId, CancellationToken.None,tenantId);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
    public class FunctionEntryPointMobileV2 : UpdateStatusHandler, IFunctionEntryPointMobileV2
    {
        public FunctionEntryPointMobileV2(IServiceProvider serviceProvider) : base(serviceProvider, typeof(FunctionEntryPointMobile).Name, "Function")
        {
        }
        public async Task SendStatusChangeNotificationAsync(UpdateStatusChangeDto request , Guid? currentUserId, string? tenantId = null)
        {
            try
            {
                await SendStatusChangeNofificationAsync(request, CancellationToken.None, currentUserId, tenantId);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
