﻿using System;
using System.Collections.Generic;

namespace BackgroundTaskExecutors.DTOs
{
    public class PFPropertyResponseDto
    {
        public string? count { get; set; }
        public string? page { get; set; }
        public string? per_page { get; set; }
        public List<PortalListedPropertyDto>? properties { get; set; }
    }

    public class PortalListedPropertyDto
    {
        public string? id { get; set; }
        public string? reference { get; set; }
        public string? size { get; set; }
        public string? bedrooms { get; set; }
        public string? bathrooms { get; set; }
        public string? furnished { get; set; }
        public PFPrice? price { get; set; }
        public List<PFPropertyInfo>? languages { get; set; }
        public string? built_up_area { get; set; }
        public string? project_status { get; set; }
        public string? project_name { get; set; }
    }
    public class PFPrice
    {
        public string? offering_type { get; set; }
        public string? value { get; set; }
    }
    public class PFPropertyInfo
    {
        public string? lang { get; set; }
        public string? title { get; set; }
        public string? description { get; set; }
    }

    public class PFAuthResponseDto
    {
        public string? access_token { get; set; }
    }

    public class LrbAssignPfPropertyDto
    {
        public Guid LeadId { get; set; }
        public string RefrenceNo { get; set; }
        public string ApiKey { get; set; }
        public string SecretKey { get; set; }
        public bool? IsPropertyListingEnable { get; set; }
    }
}
