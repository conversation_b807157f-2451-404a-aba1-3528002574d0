﻿using AWS.Logger;
using AWS.Logger.SeriLog;
using Dapper;
using Finbuckle.MultiTenant;
using Lrb.Application;
using Lrb.Application.Common.Persistence;
using Lrb.BulkLeadUpload.Lambda.Configurations;
using Lrb.Infrastructure;
using Lrb.Infrastructure.Multitenancy;
using Lrb.Infrastructure.Persistence;
using Lrb.Infrastructure.Persistence.Repository;
using Lrb.Shared.Logger;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Npgsql;
using Serilog;

namespace ExcelUpload
{
    public class Startup
    {
        private readonly IConfigurationRoot Configuration;

        public Startup()
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "qa"; //value comming from lambda environment variable
            Console.WriteLine($"Environment: {environmentName}");
            Configuration = new ConfigurationBuilder() // ConfigurationBuilder() method requires Microsoft.Extensions.Configuration NuGet package
                .SetBasePath(Directory.GetCurrentDirectory())  // SetBasePath() method requires Microsoft.Extensions.Configuration.FileExtensions NuGet package
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true) // AddJsonFile() method requires Microsoft.Extensions.Configuration.Json NuGet package
                .AddJsonFile($"appsettings.{environmentName}.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables() // AddEnvironmentVariables() method requires Microsoft.Extensions.Configuration.EnvironmentVariables NuGet package
                .AddConfigurations(environmentName)
                .Build();
        }

        public IServiceProvider ConfigureServices(string tenantId)
        {
            var services = new ServiceCollection(); // ServiceCollection require Microsoft.Extensions.DependencyInjection NuGet package

            ConfigureLoggingAndConfigurations(services);

            ConfigureApplicationServices(services);
            #region Logger
            //builder.Host.UseSerilog((_, config) =>
            //{
            //    config.WriteTo.Console()
            //    .ReadFrom.Configuration(builder.Configuration);
            //});
            AWSLoggerConfig configuration = new()
            {
                Region = Configuration["Serilog:Region"],
                Credentials = new SerilogAWSCredentials(),
                LogGroup = Configuration["Serilog:LogGroup"],
            };

            //Initialize Logger
            var logger = Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(Configuration)
                .WriteTo.AWSSeriLog(configuration)
                .CreateLogger();

            //services.AddLogging(i => i.AddSerilog(logger));
            #endregion
            //Added required services for Leads Bulk Upload
            services.AddSingleton<Serilog.ILogger>(logger);
            services.AddInfrastructureForLambda(Configuration, tenantId);
            var tenantInfo = new LrbTenantInfo() { Id = tenantId, Identifier = tenantId, Name = tenantId, ConnectionString = GetConnectionString(services, tenantId) };
            services.AddSingleton<ITenantInfo>(tenantInfo);
            IServiceProvider provider = services.BuildServiceProvider();
            if (tenantInfo is not null)
            {
                provider.GetRequiredService<IMultiTenantContextAccessor>()
                    .MultiTenantContext = new MultiTenantContext<LrbTenantInfo>
                    {
                        TenantInfo = tenantInfo
                    };
            }
            provider.ConfigureMappings();
            provider.Conig();
            return provider;
        }
        public string GetConnectionString(IServiceCollection services, string tenantId)
        {
            try
            {
                var defaultConnection = (services?.BuildServiceProvider()?.GetService<IOptions<DatabaseSettings>>()?.Value.ConnectionString) ?? string.Empty;
                var conn = new NpgsqlConnection(defaultConnection);
                try
                {
                    conn.Open();
                    var connectionString = conn.QueryFirstOrDefault<string>($"SELECT \"ConnectionString\" FROM \"MultiTenancy\".\"Tenants\" where \"Id\" = '{tenantId}'");
                    return string.IsNullOrEmpty(connectionString) ? defaultConnection : connectionString;
                }
                catch (Exception e)
                {
                    return defaultConnection;
                }

                finally
                {
                    conn.Close();
                }
            }
            catch (Exception ex)
            {
                //ignore
            }
            return string.Empty;
        }

        private void ConfigureLoggingAndConfigurations(ServiceCollection services)
        {

            // Add configuration service
            services.AddSingleton<IConfiguration>(Configuration);
            AWSLoggerConfig configuration = new()
            {
                Region = Configuration["Serilog:Region"],
                Credentials = new SerilogAWSCredentials(),
                LogGroup = Configuration["Serilog:LogGroup"],
            };
            var logger = Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(Configuration)
                .WriteTo.AWSSeriLog(configuration)
                .CreateLogger();

            // Add logging service
            services.AddLogging(loggingBuilder =>  // AddLogging() requires Microsoft.Extensions.Logging NuGet package
            {
                loggingBuilder.ClearProviders();
                loggingBuilder.AddConsole();
                loggingBuilder.AddSerilog(logger);// AddConsole() requires Microsoft.Extensions.Logging.Console NuGet package
            });

            //services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly()).AddMediatR(Assembly.GetEntryAssembly());
            services.AddApplication();
            services.AddOptions<DatabaseSettings>()
                .BindConfiguration(nameof(DatabaseSettings));
        }

        private void ConfigureApplicationServices(ServiceCollection services)
        {
            #region AWS SDK setup
            // Get the AWS profile information from configuration providers
            //AWSOptions awsOptions = Configuration.GetAWSOptions();

            // Configure AWS service clients to use these credentials
            //services.AddDefaultAWSOptions(awsOptions);

            // These AWS service clients will be singleton by default
            //services.AddAWSService<IAmazonS3>();

            //Application service
            services.AddSingleton<ITenantIndependentRepository, TenantIndependentRepository>();
            #endregion
            services.AddSingleton<IFunctionEntryPoint, FunctionEntryPoint>();
        }
    }
}
