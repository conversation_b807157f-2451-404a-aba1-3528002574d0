﻿using Lrb.Application.Agency.Web;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.Facebook;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.Integration.Web.Automation;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Integration.Web.Requests.Facebook;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;
using System.Threading;
using MessagePack;
using Lrb.Application.Common.TimeZone;
using Lrb.Application.Utils;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint : FBCommonHandler, IFunctionEntryPoint
    {
        private bool _isDupicateUnassigned = false;

        public async Task FacebookBulkLeadsFetchHandler(InputPayload input)
        {
            var tenantId = input.TenantId;
            var userId = input.CurrentUserId;
            var trackerId = input.TrackerId;
            if (!string.IsNullOrWhiteSpace(tenantId))
            {
                GlobalSettings? globalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec());
                var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(default));
                var tracker = await _fbBulkLeadFetchTrackerRepository.GetByIdAsync(trackerId);
                if (tracker == null)
                {
                    return;
                }
                tracker.LastModifiedBy = userId;

                //tracker Update
                tracker.Status = UploadStatus.Started;
                await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
                Console.WriteLine($"FacebookBulkLeadsFetchHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                try
                {
                    var subscribedAdsAndForms = await _facebookService.GetAdsAndFormsWithUserOrPageAccessTokenAsync(tenantId);
                    //tracker Update
                    tracker.ActiveAdsCount = subscribedAdsAndForms?.Count(i => i?.Type?.Contains("ad", StringComparison.InvariantCultureIgnoreCase) ?? false) ?? 0;
                    tracker.ActiveFormsCount = subscribedAdsAndForms?.Count(i => i?.Type?.Contains("form", StringComparison.InvariantCultureIgnoreCase) ?? false) ?? 0;
                    tracker.Status = UploadStatus.InProgress;
                    await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
                    Console.WriteLine($"FacebookBulkLeadsFetchHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                    if (subscribedAdsAndForms?.Any() ?? false)
                    {
                        var fromDate = tracker.FromDate;
                        var toDate = tracker.ToDate;
                        var timeZone = JsonConvert.DeserializeObject<CommonTimeZoneDto>(input.JsonData ?? string.Empty) ?? new();
                        if (tracker?.FromDate != null)
                        {
                            fromDate = ToParticularTimeZone(tracker.FromDate ?? DateTime.UtcNow.Date.ConvertFromDateToUtc(), timeZone.TimeZoneId, timeZone.BaseUTcOffset);

                        }
                        if (tracker?.ToDate != null)
                        {
                            toDate = ToParticularTimeZone(tracker.ToDate ?? DateTime.UtcNow, timeZone.TimeZoneId, timeZone.BaseUTcOffset);
                        }
                        subscribedAdsAndForms = subscribedAdsAndForms.OrderBy(x => x.Type).ToList();
                        int fetchedLeadsCount = 0;
                        int uniqueLeadsCount = 0;
                        int storedLeadsCount = 0;
                        List<LeadHistory> leadHistories = new();
                        List<Lead> allLeads = new();
                        List<FacebookAdsInfo> ads = new();
                        List<FacebookLeadGenForm> forms = new();
                        List<IntegrationAccountInfo> integrationAccounts = new();
                        List<Lead> totalFetchedFbLeads = new();
                        var customStatus = (await _customMastereadStatus.FirstOrDefaultAsync(new GetDefaultStatusSpec()));
                        foreach (var adOrForm in subscribedAdsAndForms)
                        {
                            var integrationAccountInfo = (await _integrationAccInfoRepo.ListAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(adOrForm.FacebookAuthResponseId), default)).FirstOrDefault();
                            var fbBulkleadInfo = await _facebookService.GetBulkLeadInfoAsync(adOrForm.FacebookId ?? string.Empty, adOrForm.FacebookUserOrPageAccessToken ?? string.Empty, fromDate, toDate);
                            var fbLeads = fbBulkleadInfo?.data ?? new();
                            if (fbLeads?.Any() ?? false)
                            {
                                //tracker Update
                                fetchedLeadsCount += fbLeads.Count;
                                tracker.FetchedLeadsCount = fetchedLeadsCount;
                                tracker.Status = UploadStatus.InProgress;
                                await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);

                                Console.WriteLine($"FacebookBulkLeadsFetchHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                                var ad = await _fbAdsRepo.GetByIdAsync(adOrForm.Id);
                                var form = await _facebookLeadGenFormRepo.GetByIdAsync(adOrForm.Id);
                                var leads = fbLeads.MapToGenericLeads(ad, form, globalSettings?.IsInstagramSourceEnabled ?? false, globalSettings);
                                totalFetchedFbLeads.AddRange(leads);
                                leads = leads.DistinctBy(leads => leads?.ContactNo ?? string.Empty)?.ToList();
                                var leadsWithoutCode = leads.Select(lead =>
                                {
                                    var contactNo = lead.ContactNo ?? string.Empty;
                                    var countryCode = lead.CountryCode ?? string.Empty;
                                    if (contactNo.StartsWith(countryCode))
                                    {
                                        contactNo = contactNo.Substring((countryCode).Length);
                                    }
                                    return new Lead { ContactNo = contactNo };
                                }).ToList();
                                List<Lead> duplicateLeads = new();
                                foreach (var lead in leadsWithoutCode)
                                {
                                    try
                                    {
                                        var duplicateContact = await _leadRepo.ListAsync(new DuplicateLeadForFacebookBulkFetchSpec(lead.ContactNo));
                                        duplicateLeads.AddRange(duplicateContact);
                                        if (duplicateContact.Count > 0)
                                        {
                                            leads?.RemoveAll(i => i.ContactNo.Contains(lead.ContactNo));
                                        }
                                    }
                                    catch
                                    {

                                    }
                                }
                                if (leads?.Any() ?? false)
                                {
                                    
                                    duplicateLeads = duplicateLeads.Where(i => FacebookIntegrationHelper.IsCretedOnSameTime(i.CreatedOn, i.CreatedOnPortal, leads.FirstOrDefault(newLead => newLead?.ContactNo?.Length >= 1 && newLead.ContactNo.Contains(i.ContactNo ?? "Invalid Number"))?.CreatedOnPortal)).ToList();
                                    var duplicateContacts = duplicateLeads.Select(i => Regex.Match(i.ContactNo ?? string.Empty, @"[0-9]+$").Value).ToList();
                                    leads = leads.Where(i => !duplicateContacts.Any(j => j.Contains(Regex.Match(i.ContactNo ?? string.Empty, @"[0-9]+$").Value))).ToList();
                                    var newLeads = leads;
                                    newLeads.RemoveAll(lead => lead == null || string.IsNullOrWhiteSpace(lead.ContactNo));
                                    if (newLeads?.Any() ?? false)
                                    {
                                        Console.WriteLine($"FacebookBulkLeadsFetchHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                                        foreach (Lead lead in newLeads)
                                        {
                                            if (lead == null)
                                            {
                                                break;
                                            }
                                            LeadHistory leadHistory = new();
                                            try
                                            {
                                                try
                                                {
                                                    string name = string.IsNullOrWhiteSpace(lead.Name) ? "Facebook Enquiry" : lead.Name.Trim();
                                                    lead.Name = name;
                                                    lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                                                }
                                                catch { }
                                                //lead.Status = newStatus?.FirstOrDefault();
                                                lead.CustomLeadStatus = customStatus ?? (await _customMastereadStatus.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), CancellationToken.None));
                                                lead.TagInfo = new();
                                                lead.AgencyName = adOrForm?.AgencyName;
                                                var agency = await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(adOrForm?.Agency ?? string.Empty));
                                                lead.Agencies = agency != null ? new List<Agency>() { agency } : lead.Agencies;
                                                lead.AccountId = integrationAccountInfo?.Id ?? Guid.Empty;
                                                #region Automation
                                                _isDupicateUnassigned = false;
                                                (UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project) userAssignmentAndProject = new();
                                                if (ad != null)
                                                {
                                                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAsync(ad.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbAdsRepo: _fbAdsRepo);
                                                }
                                                else if (form != null)
                                                {
                                                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAsync(form.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbFormRepo: _facebookLeadGenFormRepo);
                                                }
                                                else if (integrationAccountInfo != null)
                                                {
                                                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAsync(integrationAccountInfo.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, integrationAccRepo: _integrationAccInfoRepo);
                                                }
                                                List<Lead> existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo ?? "Invalid Number" })) ?? new();
                                                (Guid AssignTo, bool IsDupicateUnassigned) assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);
                                                lead.AssignTo = assignToRes.AssignTo;
                                                Console.WriteLine("ProcessFacebookWebhookRequestHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                                _isDupicateUnassigned = assignToRes.IsDupicateUnassigned;

                                                if (lead.Projects != null && userAssignmentAndProject.Project != null)
                                                {
                                                    lead.Projects.Add(userAssignmentAndProject.Project);
                                                }
                                                else if (userAssignmentAndProject.Project != null)
                                                {
                                                    lead.Projects ??= new List<Lrb.Domain.Entities.Project>() { userAssignmentAndProject.Project };
                                                }
                                                #endregion
                                                try
                                                {
                                                    lead.Id = Guid.NewGuid();
                                                    lead.CreatedBy = userId;
                                                    lead.LastModifiedBy = userId;
                                                    Console.WriteLine($"FacebookBulkLeadsFetchHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                                                }
                                                catch (Exception ex)
                                                {
                                                    Console.WriteLine($"Lead {lead.Name}({lead.ContactNo}) will not be added in Tenant : {tenantId}, Exception Message: {ex?.InnerException?.Message ?? ex?.Message}");
                                                    var error = new LrbError()
                                                    {
                                                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                                        ErrorSource = ex?.Source,
                                                        StackTrace = ex?.StackTrace,
                                                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                                    };
                                                    await _leadRepositoryAsync.AddErrorAsync(error);
                                                    throw;
                                                }

                                                var existingContacts = allLeads.Select(i => i.ContactNo).ToList();
                                                var contactWithoutCountryCode = lead.ContactNo.StartsWith(lead.CountryCode) ? lead.ContactNo.Substring(lead.CountryCode.Length) : lead.ContactNo;
                                                if (!existingContacts.Any(contact => contact.EndsWith(contactWithoutCountryCode)))
                                                {
                                                    allLeads.Add(lead);
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                var error = new LrbError()
                                                {
                                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                                    ErrorSource = ex?.Source,
                                                    StackTrace = ex?.StackTrace,
                                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                                };
                                                await _leadRepositoryAsync.AddErrorAsync(error);
                                            }
                                        }
                                        if (ad != null)
                                        {
                                            ads.Add(ad);
                                        }
                                        else if (form != null)
                                        {
                                            forms.Add(form);
                                        }
                                        if (integrationAccountInfo != null)
                                        {
                                            integrationAccounts.Add(integrationAccountInfo);
                                        }
                                    }
                                }
                            }
                        }
                        List<Lead> totalExistingLeadsInDateRange = await _leadRepo.ListAsync(new LeadsBySourceAndDateRangeSpec(fromDate ?? default, toDate ?? default, LeadSource.Facebook));
                        var totalDistinctExistingLeadsInDateRange = totalExistingLeadsInDateRange.DistinctBy(i => i.ContactNo).ToList();
                        Console.WriteLine($"Total unique leads present the DB in the date range given = {totalDistinctExistingLeadsInDateRange.Count}");
                        var totalFetchedUniqueLeads = totalFetchedFbLeads.DistinctBy(i => i.ContactNo).ToList();
                        Console.WriteLine($"Total fetched unique leads from Facebook in the date range given = {totalDistinctExistingLeadsInDateRange.Count}");
                        Console.WriteLine($"The difference between Fetched Leads and Existing Leads in the date range given, Unique(Fetched - Existing) = {totalDistinctExistingLeadsInDateRange.Count - totalDistinctExistingLeadsInDateRange.Count}");
                        var uniqueLeads = allLeads.DistinctBy(i => i.ContactNo).ToList();
                        Console.WriteLine($"Total unique and valid leads from Facebook after removing the leads, which were created in same time on Facebook and CRM = {uniqueLeads.Count}");
                        var uniqueLeadDtos = uniqueLeads.Adapt<List<ViewLeadDto>>();
                        leadHistories = uniqueLeadDtos.Select(i => LeadHistoryHelper.LeadHistoryMapper(i)).ToList();
                        foreach (var lead in uniqueLeads)
                        {
                            #region DuplicateDetails
                            var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), default);
                            if (parentLead != null)
                            {
                                lead.RootId = parentLead.Id;
                                lead.DuplicateLeadVersion = "D" + (parentLead.ChildLeadsCount + 1);
                                lead.ParentLeadId = parentLead.Id;
                                parentLead.ChildLeadsCount += 1;
                                try
                                {
                                    await _leadRepo.UpdateAsync(parentLead);
                                }
                                catch { }
                            }
                            #endregion
                        }
                        await _leadRepo.AddRangeAsync(uniqueLeads);
                        await _leadHistoryRepo.AddRangeAsync(leadHistories);

                        ads = ads.DistinctBy(i => i.Id).ToList();
                        forms = forms.DistinctBy(i => i.Id).ToList();
                        integrationAccounts = integrationAccounts.DistinctBy(i => i.Id).ToList();
                        var platfromCountDtos = subscribedAdsAndForms.Select(i => new FbPlatformCountDto
                        {
                            FacebookAuthResponseId = i.FacebookAuthResponseId
                        }).ToList();
                        foreach (var adOrForm in subscribedAdsAndForms)
                        {
                            var ad = ads.FirstOrDefault(i => i.Id == adOrForm.Id);
                            if (ad != null)
                            {
                                var relatedLeads = uniqueLeads.Where(i => (i.Notes?.Contains(ad.AdName ?? "Invalid Name", StringComparison.InvariantCultureIgnoreCase) ?? false) || (i.ConfidentialNotes?.Contains(ad.AdName ?? "Invalid Name", StringComparison.InvariantCultureIgnoreCase) ?? false));
                                ad.LeadsCount += relatedLeads.Count();
                                var platformCountDto = platfromCountDtos.FirstOrDefault(i => i.FacebookAuthResponseId == ad.FacebookAuthResponseId);
                                if (platformCountDto != null)
                                {
                                    platformCountDto.InstaLeadCount += relatedLeads.Where(i => i.Enquiries?[0]?.LeadSource == LeadSource.Instagram).Count();
                                    platformCountDto.FbLeadCount += relatedLeads.Where(i => i.Enquiries?[0]?.LeadSource == LeadSource.Facebook).Count();
                                }
                            }
                            var form = forms.FirstOrDefault(i => i.Id == adOrForm.Id);
                            if (form != null)
                            {
                                var relatedLeads = uniqueLeads.Where(i => (i.Notes?.Contains(form.Name, StringComparison.InvariantCultureIgnoreCase) ?? false) || (i.ConfidentialNotes?.Contains(form.Name, StringComparison.InvariantCultureIgnoreCase) ?? false));
                                form.TotalLeadsCount += relatedLeads.Count();
                                var platformCountDto = platfromCountDtos.FirstOrDefault(i => i.FacebookAuthResponseId == adOrForm.FacebookAuthResponseId);
                                if (platformCountDto != null)
                                {
                                    platformCountDto.InstaLeadCount += relatedLeads.Where(i => i.Enquiries?[0]?.LeadSource == LeadSource.Instagram).Count();
                                    platformCountDto.FbLeadCount += relatedLeads.Where(i => i.Enquiries?[0]?.LeadSource == LeadSource.Facebook).Count();
                                }
                            }
                        }
                        foreach (var intgrAcc in integrationAccounts)
                        {
                            try
                            {
                                intgrAcc.LeadCount += uniqueLeads?.Count() ?? 0;

                                /* var relatedAds = ads.Where(i => i.FacebookAuthResponseId == intgrAcc.FacebookAccountId).ToList();
                                 intgrAcc.LeadCount += relatedAds?.Sum(i => i.LeadsCount) ?? 0;

                                 var relatedForms = forms.Where(i => subscribedAdsAndForms.Where(j => j.FacebookAuthResponseId == intgrAcc.FacebookAccountId).Select(k => k.Id).ToList().Contains(i.Id)).ToList();
                                 intgrAcc.LeadCount += relatedForms?.Sum(i => i.TotalLeadsCount) ?? 0;*/
                            }
                            catch (Exception e) { }
                        }
                        foreach (var platformDto in platfromCountDtos)
                        {
                            var fbAuthResponse = await _facebookAuthResponseRepo.GetByIdAsync(platformDto.FacebookAuthResponseId);
                            if (fbAuthResponse != null && (platformDto.InstaLeadCount > 0 || platformDto.FbLeadCount > 0))
                            {
                                fbAuthResponse.InstaLeadCount += platformDto.InstaLeadCount;
                                fbAuthResponse.FbLeadCount += platformDto.FbLeadCount;
                                await _facebookAuthResponseRepo.UpdateAsync(fbAuthResponse);
                            }
                        }
                        await _fbAdsRepo.UpdateRangeAsync(ads);
                        await _facebookLeadGenFormRepo.UpdateRangeAsync(forms);
                        await _integrationAccInfoRepo.UpdateRangeAsync(integrationAccounts);
                        uniqueLeadsCount += uniqueLeads.Count;
                        storedLeadsCount += uniqueLeads.Count;
                        tracker.FetchedLeadsCount = fetchedLeadsCount;
                        tracker.UniqueLeadsCount = uniqueLeadsCount;
                        tracker.StoredLeadsCount = storedLeadsCount;
                        tracker.Status = UploadStatus.Completed;
                        await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
                        Console.WriteLine($"FacebookBulkLeadsFetchHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine($"FacebookBulkLeadsFetchHandler() Error Message: {e?.InnerException?.Message ?? e?.Message}");
                    tracker.Status = UploadStatus.Failed;
                    tracker.Error = JsonConvert.SerializeObject(e, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore });
                    await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
                    var error = new LrbError()
                    {
                        ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                        ErrorSource = e?.Source,
                        StackTrace = e?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "FunctionEntryPoint -> FacebookBulkLeadsFetchHandler()",
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
            }
        }
        private async Task SendNotificationsForBulkFacebookLeadsAsync(BulkUploadbackgroundDto dto)
        {
            Console.WriteLine("BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() called -> BulkUploadbackgroundDto -> UserIds = " + JsonConvert.SerializeObject(dto.UserIds, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
            Console.WriteLine("BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() called -> BulkUploadbackgroundDto -> CurrentUserId = " + JsonConvert.SerializeObject(dto.CurrentUserId, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
            if (dto.UserIds?.Any(i => Guid.Parse(i) != Guid.Empty) ?? false)
            {
                var userIdsWithNoOfLeadsAssigned = dto.Leads?.Where(i => i.AssignTo != Guid.Empty).GroupBy(i => i.AssignTo).ToDictionary(i => i.Key, j => j.Count());
                if (userIdsWithNoOfLeadsAssigned?.Count > 0)
                {
                    foreach (var item in userIdsWithNoOfLeadsAssigned)
                    {
                        try
                        {
                            var userDetails = dto.Users?.FirstOrDefault(i => i.Id == item.Key);
                            if (userDetails != null && userDetails.Id != dto.CurrentUserId)
                            {
                                var lead = dto.Leads?.FirstOrDefault(i => i.AssignTo == item.Key);
                                Console.WriteLine($"BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() -> Event: {Event.FacebookBulkLeadsFetch}, lead: {JsonConvert.SerializeObject(lead, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, UserDetails: {JsonConvert.SerializeObject(userDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, NoOfLeads: {item.Value}");
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.FacebookBulkLeadsFetch, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, null, item.Value, dto.CurrentUserId);
                                Console.WriteLine($"BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() -> Response: {JsonConvert.SerializeObject(notificationSchduleResponse, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, UserDetails: {JsonConvert.SerializeObject(userDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, NoOfLeads: {item.Value}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "FunctionEntryPoint -> SendNotificationsForBulkFacebookLeadsAsync()",
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                }
            }
            else
            {
                var unAssignedLeadsCount = dto.UserIds?.Where(i => Guid.Parse(i) == Guid.Empty)?.Count() ?? 0;
                if (unAssignedLeadsCount > 0)
                {
                    List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(dto.TenantInfoDto?.Id ?? string.Empty);
                    var leadForNotification = dto.Leads?.FirstOrDefault();
                    if (adminIds != null && adminIds.Any())
                    {
                        foreach (var adminId in adminIds)
                        {
                            var adminDetails = await _userService.GetAsync(adminId.ToString(), dto.CancellationToken);
                            if (adminDetails != null)
                            {
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.UnAssignedLeadUpdate, leadForNotification, adminId, adminDetails.FirstName + " " + adminDetails.LastName, topics: new List<string> { leadForNotification?.CreatedBy.ToString() ?? string.Empty, leadForNotification?.LastModifiedBy.ToString() ?? string.Empty }, unAssignedLeadsCount);
                            }
                        }
                    }
                }
            }
        }
        private async Task<(Guid, List<Lrb.Domain.Entities.Project>)> AutomateLeadAsync(Lead lead, Guid? automationId)
        {
            Console.WriteLine("FacebookBulkLeadsFetchHandler -> Mapped Lead before assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
            var assignmentInfo = await _integrationAssignmentInfoRepo.GetByIdAsync(automationId ?? Guid.Empty);
            if (assignmentInfo?.ProjectIds?.Any() ?? false)
            {
                var project = (await _newProjectRepo.ListAsync(new GetAllProjectsV2Spec(assignmentInfo?.ProjectIds ?? new()), CancellationToken.None))?.FirstOrDefault();
                lead.Projects ??= new List<Lrb.Domain.Entities.Project>();
                if (project != null)
                {
                    lead.Projects.Add(project);
                    if (project.IsAutomated || project.AutomationId != default)
                    {
                        var projAssignmentInfo = await _integrationAssignmentInfoRepo.GetByIdAsync(project.AutomationId);
                        if (projAssignmentInfo?.AssignedUserIds?.Any() ?? false)
                        {
                            assignmentInfo = projAssignmentInfo;
                        }
                    }
                }
            }
            lead = await lead.GetAssignedLead(assignmentInfo?.Id ?? Guid.Empty, _integrationAssignmentInfoRepo, _userDetailsRepo, _userService);
            Console.WriteLine("FacebookBulkLeadsFetchHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
            return (lead.AssignTo, lead?.Projects?.ToList() ?? new());
        }
        private static DateTime ToParticularTimeZone(DateTime utcDate, string? timeZoneId, TimeSpan baseUTcOffset = default)
        {
            if (timeZoneId != null && baseUTcOffset != default)
            {
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                if (timeZone.BaseUtcOffset.TotalSeconds < 0)
                {
                    return utcDate + baseUTcOffset;
                }
                else
                {
                    return utcDate + baseUTcOffset;
                }
            }
            else
            {
                return utcDate;
            }
        }
    }
}
