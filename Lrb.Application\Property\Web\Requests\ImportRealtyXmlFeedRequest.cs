using Lrb.Application.Property.Web.Dtos.XMl_Feed;
using Lrb.Application.Property.Web.Services;
using Lrb.Domain.Enums;
using Mapster;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Common.Models;
using Lrb.Application.Property.Web;

namespace Lrb.Application.Property.Web.Requests
{
    public class ImportRealtyXmlFeedRequest : IRequest<Response<ImportRealtyXmlFeedResponseDto>>
    {
        public string Url { get; set; } = string.Empty;
        public string? TenantId { get; set; }
        public Guid? CurrentUserId { get; set; }
    }

    public class ImportRealtyXmlFeedResponseDto
    {
        public int TotalOffers { get; set; }
        public int ProcessedOffers { get; set; }
        public int SuccessfulImports { get; set; }
        public int FailedImports { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<Guid> CreatedPropertyIds { get; set; } = new();
    }

    public class ImportRealtyXmlFeedRequestHandler : IRequestHandler<ImportRealtyXmlFeedRequest, Response<ImportRealtyXmlFeedResponseDto>>
    {
        private readonly IRealtyXmlFeedService _xmlFeedService;
        private readonly IMediator _mediator;
        private readonly Serilog.ILogger _logger;

        public ImportRealtyXmlFeedRequestHandler(
            IRealtyXmlFeedService xmlFeedService,
            IMediator mediator,
            Serilog.ILogger logger)
        {
            _xmlFeedService = xmlFeedService;
            _mediator = mediator;
            _logger = logger;
        }

        public async Task<Response<ImportRealtyXmlFeedResponseDto>> Handle(ImportRealtyXmlFeedRequest request, CancellationToken cancellationToken)
        {
            var response = new ImportRealtyXmlFeedResponseDto();

            try
            {
                _logger.Information("Starting XML feed import from URL: {Url}", request.Url);

                // Fetch and parse XML feed
                var realtyFeed = await _xmlFeedService.FetchRealtyFeedAsync(request.Url, cancellationToken);

                if (realtyFeed?.Offers == null || !realtyFeed.Offers.Any())
                {
                    _logger.Warning("No offers found in XML feed from URL: {Url}", request.Url);
                    return new Response<ImportRealtyXmlFeedResponseDto>(response);
                }

                response.TotalOffers = realtyFeed.Offers.Count;
                _logger.Information("Found {TotalOffers} offers in XML feed", response.TotalOffers);

                // Process each offer
                foreach (var offer in realtyFeed.Offers)
                {
                    response.ProcessedOffers++;

                    try
                    {
                        var createPropertyDto = MapOfferToCreatePropertyDto(offer);
                        
                        if (createPropertyDto != null)
                        {
                            var createPropertyRequest = createPropertyDto.Adapt<CreatePropertyRequest>();
                            createPropertyRequest.CreatedBy = request.CurrentUserId ?? Guid.Empty;
                            createPropertyRequest.LastModifiedBy = request.CurrentUserId ?? Guid.Empty;

                            // Ensure all DateTime properties are UTC for PostgreSQL compatibility
                            EnsureUtcDateTimes(createPropertyRequest);

                            var result = await _mediator.Send(createPropertyRequest, cancellationToken);

                            if (result?.Data?.Id != null)
                            {
                                response.SuccessfulImports++;
                                response.CreatedPropertyIds.Add(result.Data.Id);
                                _logger.Information("Successfully created property with ID: {PropertyId} from offer: {ComplexId}", 
                                    result.Data.Id, offer.ComplexId);
                            }
                            else
                            {
                                response.FailedImports++;
                                var error = $"Failed to create property from offer {offer.ComplexId}: No property ID returned";
                                response.Errors.Add(error);
                                _logger.Warning(error);
                            }
                        }
                        else
                        {
                            response.FailedImports++;
                            var error = $"Failed to map offer {offer.ComplexId} to CreatePropertyDto";
                            response.Errors.Add(error);
                            _logger.Warning(error);
                        }
                    }
                    catch (Exception ex)
                    {
                        response.FailedImports++;
                        var error = $"Error processing offer {offer.ComplexId}: {ex.Message}";
                        response.Errors.Add(error);
                        _logger.Error(ex, "Error processing offer {ComplexId}", offer.ComplexId);
                    }
                }

                _logger.Information("XML feed import completed. Successful: {Successful}, Failed: {Failed}", 
                    response.SuccessfulImports, response.FailedImports);

                return new Response<ImportRealtyXmlFeedResponseDto>(response);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during XML feed import from URL: {Url}", request.Url);
                response.Errors.Add($"Import failed: {ex.Message}");
                return new Response<ImportRealtyXmlFeedResponseDto>(response);
            }
        }

        private CreatePropertyDto? MapOfferToCreatePropertyDto(RealtyOfferDto offer)
        {
            try
            {
                var createPropertyDto = new CreatePropertyDto
                {
                    // Map Title (prefer English, fallback to Russian, then Arabic)
                    Title = GetPreferredLanguageText(offer.Title),
                    
                    // Map Description to AboutProperty
                    AboutProperty = GetPreferredLanguageText(offer.Description),
                    
                    // Map Status
                    Status = MapStatus(offer.Status),
                    
                    // Map SaleType - assuming all are for sale based on XML structure
                    SaleType = SaleType.New,
                    
                    // Map EnquiredFor - assuming residential based on type
                    EnquiredFor = EnquiryType.Buy,
                    
                    // Map PropertySource
                    PropertySource = PropertySource.BUILDER,
                    
                    // Map Project name
                    Project = GetPreferredLanguageText(offer.Title),
                    
                    // Map SerialNo to ComplexId
                    SerialNo = offer.ComplexId,
                    
                    // Map RefrenceNo to ComplexId
                    RefrenceNo = offer.ComplexId,
                    
                    // Map completion status
                    CompletionStatus = MapCompletionStatus(offer.Status),
                    
                    // Map possession date
                    PossessionDate = ParseDateTimeUtc(offer.PredictedCompletionAt ?? offer.PlannedCompletionAt),
                    
                    // Map additional properties
                    AdditionalProperties = new Dictionary<string, string>
                    {
                        ["ComplexId"] = offer.ComplexId ?? "",
                        ["Type"] = offer.Type ?? "",
                        ["Logo"] = offer.Logo ?? "",
                        ["Photo"] = offer.Photo ?? "",
                        ["ConstructionStartAt"] = offer.ConstructionStartAt ?? "",
                        ["ConstructionProgress"] = offer.ConstructionProgress ?? "",
                        ["PlannedCompletionAt"] = offer.PlannedCompletionAt ?? "",
                        ["PredictedCompletionAt"] = offer.PredictedCompletionAt ?? "",
                        ["BuildingsCount"] = offer.BuildingsCount ?? "",
                        ["ForSaleCount"] = offer.ForSaleCount ?? "",
                        ["IsSoldOut"] = offer.IsSoldOut ?? "",
                        ["UpdatedAt"] = offer.UpdatedAt ?? "",
                        ["Developer"] = GetPreferredLanguageText(offer.Developer?.Title) ?? "",
                        ["SalesStatus"] = GetPreferredLanguageText(offer.SalesStatus) ?? ""
                    }
                };

                // Map Address
                if (!string.IsNullOrEmpty(offer.Address))
                {
                    createPropertyDto.Address = new AddressDto
                    {
                        City = offer.Address,
                        Latitude = offer.Latitude,
                        Longitude = offer.Longitude
                    };
                }

                // Map Images
                if (offer.Album?.Images?.Any() == true)
                {
                    createPropertyDto.ImageUrls = new Dictionary<string, List<PropertyGalleryDto>>
                    {
                        ["External"] = offer.Album.Images.Select(img => new PropertyGalleryDto
                        {
                            ImageFilePath = img,
                            IsCoverImage = false
                        }).ToList()
                    };
                }

                // Map Amenities (convert to additional properties since we don't have direct mapping)
                if (offer.Amenities?.Amenity?.Any() == true)
                {
                    var amenitiesText = string.Join(", ", offer.Amenities.Amenity
                        .Select(a => GetPreferredLanguageText(a))
                        .Where(text => !string.IsNullOrEmpty(text)));
                    
                    if (!string.IsNullOrEmpty(amenitiesText))
                    {
                        createPropertyDto.AdditionalProperties["Amenities"] = amenitiesText;
                    }
                }

                // Map Price information
                if (offer.Price != null)
                {
                    createPropertyDto.MonetaryInfo = new PropertyMonetaryInfoDto
                    {
                        ExpectedPrice = ParseLong(offer.Price.Min) ?? 0,
                        Currency = offer.Price.Currency ?? "AED"
                    };
                }

                // Map bedroom information from br_prices
                if (offer.BedroomPrices?.Any() == true)
                {
                    var firstBedroomInfo = offer.BedroomPrices.First();
                    if (firstBedroomInfo.Key?.Contains("rooms_") == true)
                    {
                        var roomsText = firstBedroomInfo.Key.Replace("rooms_", "");
                        if (double.TryParse(roomsText, out var bhkCount))
                        {
                            createPropertyDto.NoOfBHK = bhkCount;
                            createPropertyDto.BHKType = bhkCount switch
                            {
                                1 => BHKType.Simplex,
                                2 => BHKType.Duplex,
                                3 => BHKType.PentHouse,
                                _ => BHKType.Others
                            };
                        }
                    }

                    // Map area information
                    if (firstBedroomInfo.MinArea != null)
                    {
                        createPropertyDto.Dimension = new PropertyDimensionDto
                        {
                            Area = ParseDouble(firstBedroomInfo.MinArea.M2) ?? 0,
                            AreaUnitId = Guid.Empty
                        };
                    }
                }

                return createPropertyDto;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error mapping offer {ComplexId} to CreatePropertyDto", offer.ComplexId);
                return null;
            }
        }

        private string? GetPreferredLanguageText(MultiLanguageTextDto? multiLangText)
        {
            if (multiLangText == null) return null;
            
            return !string.IsNullOrEmpty(multiLangText.English) ? multiLangText.English :
                   !string.IsNullOrEmpty(multiLangText.Russian) ? multiLangText.Russian :
                   multiLangText.Arabic;
        }

        private PropertyStatus MapStatus(MultiLanguageTextDto? status)
        {
            var statusText = GetPreferredLanguageText(status)?.ToLowerInvariant();
            
            return statusText switch
            {
                "under construction" or "строится" or "تحت التشيد" => PropertyStatus.Active,
                "completed" or "завершено" or "مكتمل" => PropertyStatus.Active,
                _ => PropertyStatus.Active
            };
        }

        private CompletionStatus? MapCompletionStatus(MultiLanguageTextDto? status)
        {
            var statusText = GetPreferredLanguageText(status)?.ToLowerInvariant();

            return statusText switch
            {
                "under construction" or "строится" or "تحت التشيد" => CompletionStatus.OffPlan,
                "completed" or "завершено" or "مكتمل" => CompletionStatus.Completed,
                _ => CompletionStatus.OffPlan
            };
        }

        private DateTime? ParseDateTime(string? dateTimeString)
        {
            if (string.IsNullOrEmpty(dateTimeString)) return null;

            return DateTime.TryParse(dateTimeString, out var result) ? result : null;
        }

        private DateTime? ParseDateTimeUtc(string? dateTimeString)
        {
            if (string.IsNullOrEmpty(dateTimeString)) return null;

            if (DateTime.TryParse(dateTimeString, out var result))
            {
                // Convert to UTC if not already UTC
                return result.Kind switch
                {
                    DateTimeKind.Utc => result,
                    DateTimeKind.Local => result.ToUniversalTime(),
                    DateTimeKind.Unspecified => DateTime.SpecifyKind(result, DateTimeKind.Utc),
                    _ => result
                };
            }

            return null;
        }

        /// <summary>
        /// Ensures all DateTime properties in the CreatePropertyRequest are UTC for PostgreSQL compatibility
        /// </summary>
        private void EnsureUtcDateTimes(CreatePropertyRequest request)
        {
            // Convert PossessionDate to UTC if it exists
            if (request.PossessionDate.HasValue)
            {
                request.PossessionDate = EnsureUtc(request.PossessionDate.Value);
            }

            // Convert any other DateTime properties that might exist
            // Note: CreatedOn and LastModifiedOn will be set by the system, but we ensure they're UTC
            if (request.CreatedOn != default)
            {
                request.CreatedOn = EnsureUtc(request.CreatedOn);
            }
            else
            {
                request.CreatedOn = DateTime.UtcNow;
            }

            if (request.LastModifiedOn.HasValue)
            {
                request.LastModifiedOn = EnsureUtc(request.LastModifiedOn.Value);
            }
            else
            {
                request.LastModifiedOn = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Ensures a DateTime is in UTC format for PostgreSQL compatibility
        /// </summary>
        private static DateTime EnsureUtc(DateTime dateTime)
        {
            return dateTime.Kind switch
            {
                DateTimeKind.Utc => dateTime,
                DateTimeKind.Local => dateTime.ToUniversalTime(),
                DateTimeKind.Unspecified => DateTime.SpecifyKind(dateTime, DateTimeKind.Utc),
                _ => dateTime
            };
        }

        private double? ParseDouble(string? value)
        {
            if (string.IsNullOrEmpty(value)) return null;
            
            return double.TryParse(value, out var result) ? result : null;
        }

        private decimal? ParseDecimal(string? value)
        {
            if (string.IsNullOrEmpty(value)) return null;

            return decimal.TryParse(value, out var result) ? result : null;
        }

        private long? ParseLong(string? value)
        {
            if (string.IsNullOrEmpty(value)) return null;

            return long.TryParse(value, out var result) ? result : null;
        }
    }
}
